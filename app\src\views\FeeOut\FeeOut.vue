<template>
  <div class="pages">
    <van-nav-bar title="费用支出" left-arrow @click-left="goback" safe-area-inset-top>
      <template #right>
        <div class="noSubmitStyle" @click="isSubmitPopup = true">结算</div>
      </template>
    </van-nav-bar>
    <div class="public_box2">
      <van-form v-model="sheet">
        <div class="public_box2_t">
          <div class="prepay_title" v-if="sheet.sheet_no">
            <span>{{sheet.sheet_no}}</span>
            <span>{{sheet.approve_time}}</span>
          </div>
        </div>
        <div class="public_query_titleSrc">
          <div class="public_query_wrapper">
            <!-- 客户/供应商/仓库/定货会账户 -->
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="user-o" @click="onSelectClient" />
              <input type="text" style=" width: calc(100% - 60px);" v-model="sheet.sup_name" :placeholder="sheet.sheetType == 'SR'  ? '供应商' : '客户'" readonly @click="onSelectClient" />
            </div> 
          </div>         
          <div v-if="canAppSelectSeller" class="public_query_wrapper">
            <div class="public_query_titleSrc_item" style="width:100%;display:flex;align-items: center;margin-left: 10px;;">
              <svg class="icon black query_icon" stroke-width="1.3" width="22px" height="22px">
                <use :xlink:href="'#icon-seller'" fill="#aaa"></use>
              </svg>
              <select-one class="selectOne" style=" width: calc(100% - 50px);" placeholder="业务员" :hasClearBtn="false" :target="'seller'" :formObj="sheet" :formFld="'getter_id'" :formNameFld="'getter_name'" />
            </div>
          </div>
          <div v-if="canAppSelectSeller" class="public_query_wrapper">
            <div class="public_query_titleSrc_item" style="width:100%">
              <van-icon name="clock-o" />
              <input type="text" style="width: calc(100% - 60px);" v-model="sheet.happen_time" placeholder="交易日期" readonly @click="onInputHappenTime" />
            </div>
          </div>
        </div>
        
        <div class="public_box2_b">
          <div class="title3">
            <div>费用类别</div>
            <div>金额</div>
          </div>
          <div class="sheet_state approved" v-if="sheet.approve_time&&(sheet.red_flag==''||sheet.red_flag=='0')">
            <img src="../../assets/images/approved.png">
          </div>
          <div class="sheet_state reded" v-if="sheet.red_flag=='1'">
            <img src="../../assets/images/reded.png">
          </div>
          <div class="cost_list" v-if="sheet.sheetRows && sheet.sheetRows.length > 0">
            <van-swipe-cell v-for="(item,index) in sheet.sheetRows" :key="index + '_' + item.fee_sub_id + '_' + item.fee_sub_amount">
              <van-cell :title="item.fee_sub_name" :value="item.fee_sub_amount" :label="item.remark" />
              <template #right>
                <van-button square type="danger" v-if="!sheet.approve_time" text="删除" @click="deleteFeeOutRow(item,index)" />
              </template>
            </van-swipe-cell>
          </div>
          <div class="cost_footer">
            <div class="cost_footer_t">
              <div class="footer_t_c" v-if="sheet.payway1_name && sheet.payway1_amount">
                <span>{{sheet.payway1_name}}: </span>
                <span>{{sheet.payway1_amount}}</span>
              </div>
              <div class="footer_t_r" v-if="sheet.payway2_name && sheet.payway2_amount">
                <span>{{sheet.payway2_name}}: </span>
                <span>{{sheet.payway2_amount}}</span>
              </div>
              <div style="color: #FF0000;" class="footer_t_r" v-if="sheet.left_amount !=='0'&&sheet.left_amount!==0 && sheet.supcust_id !==''">
                <span style="white-space: nowrap;">欠款: </span>
                <span>{{sheet.left_amount}}</span>
              </div>
              <div class="footer_t_l">
                <span style="white-space: nowrap;">合计: </span>
                <span>{{sheet.total_amount}}</span>
              </div>
            </div>
            <div class="cost_footer_b">
 
              <div style="margin-right:20px;margin-bottom:10px;" class="footer_iconBt" :disabled="sheet.approve_time || IsSubmiting" @click="showFeeOut = true">
                <svg width="40px" height="40px" fill="#F56C6C">
                  <use xlink:href="#icon-add"></use>
                </svg>
              </div>
              <van-button class="button" v-show="displaySheets.length > 0" :disabled="sheet.sheet_no!==''" color="#3793df" plain @click="handleShowDisplay">陈列费用</van-button>
            </div>
          </div>
        </div>
        <van-popup v-model="showCustomer" style="overflow: hidden!important;" position="bottom" :duration="0.3" :style="{ height: '85%', width: '100%' }">
          <SelectCustomer @onClientSelected="onClientSelected"></SelectCustomer>
        </van-popup>
        <van-popup v-model="showFeeOut" style="overflow: hidden!important;" position="bottom" :duration="0.3" :style="{ height: '60%', width: '100%' }">
          <AddFeeOut @closeFeeOutPop='showFeeOut = false' @addFeeout="addFeeout"></AddFeeOut>
        </van-popup>
        <van-popup v-model="showDisplay" style="overflow: hidden!important;" position="bottom" :duration="0.3" :style="{ height: '100%', width: '100%' }">
          <fee-out-display-money v-if="showDisplay" :display-sheets="displaySheets" @handleClickCancelDisplay="handleClickCancelDisplay" @handleConfirmClickDisplay="handleConfirmClickDisplay" />
        </van-popup>
      </van-form>
    </div>
    <van-popup v-model="isSubmitPopup" class="other_popup" duration="0.4" :style="{ height: '100%', width: '80%' }" position="right">
      <!-- <h5 class="custom_h5">
        其他选项
        <van-icon
          class="icon_h5"
          name="cross"
          @click="isSubmitPopup = false"
        />
      </h5> -->

      <div class="other_operate">
        <div class="other_operate_total">
          <span style="white-space: nowrap;">合计: </span>
          <span>￥{{sheet.total_amount}}</span>
        </div>
        <div style="display: flex; flex-direction: row" v-if="canAllowSheetArrears&&sheet.sup_name">
          <van-field style="white-space: nowrap;" v-model="sheet.left_amount" label="欠款" label-width="40px" type="number" placeholder="欠款" :formatter="checkInputLegalSymbol" format-trigger="onBlur" :disabled="sheet.approve_time ? true : false" @input="onLeftAmountChange" />
          <div style="margin-top:12px;margin-bottom:0px;background-color: #fff" class="discount-btn">元</div>
        </div>
        <van-field v-model="sheet.make_brief" label="备注" label-width="40px" style="white-space: nowrap;" :disabled="sheet.approve_time ? true : false" />
        <van-divider style="margin: 10px 0px 20px;">附件</van-divider>
        <div class="other_operate_content">
          <div class="other_operate_content_left">
            <div class="appendixphoto-container" v-for="item,index in sheet.appendixPhotos" :key="index">
              <img @click="preview_photo(index)" class="photo" :src="item">
              <div v-if="sheet.approve_time == '' && !IsSubmiting" class="remove-icon" @click="remove_photo(index)"><van-icon name="close" /></div>
            </div>
          </div>
          <div class="other_operate_content_right">
            <div class="iconfont" v-if="sheet.approve_time == '' && !IsSubmiting" @click="showImageSelect = true">&#xe62e;</div>
          </div>
        </div>
        <div class="payway">

          <van-row class="payway">
            <van-col span="9" @click="selectBillWay(1)">{{sheet.payway1_name? sheet.payway1_name:'支付方式'}}</van-col>
            <van-col span="10">
              <input type="number" :readonly="sheet.approve_time !== ''" v-model="sheet.payway1_amount" placeholder="请输入金额" @input="firstPayway">
            </van-col>
            <van-col span="5">
              <van-icon v-if="!sheet.approve_time && !payway2" class="payway_add" name="plus" @click="handlePayWay(true)" />
            </van-col>
          </van-row>

          <van-row class="payway" v-if="payway2">
            <van-col span="9" @click="selectBillWay(0)">{{sheet.payway2_name? sheet.payway2_name:'支付方式'}}</van-col>
            <van-col span="10">
              <input type="number" :readonly="sheet.approve_time !== ''" v-model="sheet.payway2_amount" placeholder="请输入金额" @input="otherPayway">
            </van-col>
            <van-col span="5">
              <van-icon v-if="!sheet.approve_time&&payway2" class="payway_add" name="minus" @click="handlePayWay(false)" />
            </van-col>
          </van-row>
        </div>

        <div class="other_operate_content" style="margin:40px 0px;">
          <button plain type="info" style="height: 45px;border-radius:12px;background-color: #fff;border:1px solid #ccc;white-space: nowrap;" v-if="canMake && sheet.approve_time ===''" :disabled="IsSubmiting" @click="btnSave_click">保存</button>
          <button plain type="info" style="height: 45px;border-radius:12px;background-color: #ffcccc;border:1px solid #ccc;white-space: nowrap;" v-if="canApprove" :disabled="sheet.approve_time!=='' || IsSubmiting" @click="btnSubmit_click">审核</button>
          <button plain type="default" style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc;white-space: nowrap;" @click="handlePrintSheet" :disabled="isPrinting">打印</button>
          <van-button plain type="danger" style="height: 45px;border-radius:12px;white-space: nowrap;" :style="{color:sheet.red_flag!==''?'#ff9999':'#ee0a24',borderColor:sheet.red_flag!==''?'#ff9999':'#ee0a24'}" v-if="canRed && sheet.sheet_id && sheet.approve_time" :disabled="sheet.red_flag!==''" @click="redBill">红冲</van-button>
          <!-- <van-button plain type="danger" style="height: 45px;border-radius:12px;white-space: nowrap;" v-if="sheet.sheet_id && !sheet.approve_time" @click="btnDeleteSheet_click">删除</van-button> -->
        </div>

        <div class="other_operate_content">
          <van-button plain type="danger" style="height: 45px;border-radius:12px;white-space: nowrap;" :disabled="sheet.sheet_id === '' || sheet.approve_time !==''" @click="btnDeleteSheet_click">删除</van-button>
          <van-button plain type="default" style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc;white-space: nowrap;" :disabled="sheet.approve_time!==''" @click="clearFeeOut">清空</van-button>
          <van-button plain type="default" style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc;white-space: nowrap;" @click="outFeeOut">退出</van-button>
        </div>
      </div>
    </van-popup>
    <van-popup v-model="bPopupClientSelectDialog" duration="0.4" position="bottom" :lock-scroll="false" :style="{ height: '100%', position: 'absolute', width: '100%', overflow: 'hidden' }">
       
      <SelectCustomer canHide='true' @handleHide='bPopupClientSelectDialog = false' ref='selectCustomer' :showAcctCusts="sheet.sheetType == 'ZC'" :sheetType="sheet.sheetType" v-if="',ZC,'.indexOf(',' + sheet.sheetType + ',') >= 0" @onClientSelected="onClientSelected">
      </SelectCustomer>
      <SelectSupplier canHide='true' @handleHide='bPopupClientSelectDialog = false' v-if="',SR,'.indexOf(',' + sheet.sheetType + ',') >= 0" @onClientSelected="onClientSelected"></SelectSupplier>

    </van-popup>
    <van-popup v-model="bPopupSelectHappenTime" round position="bottom" :style="{ height: '40%' }">
      <date-picker v-if="bPopupSelectHappenTime" 
        v-model="sheet.happen_time"
        :inline="false"
        :append-to-body="false"
        format="YYYY-MM-DD"
        type="datetime"
        value-type="format"
        :open="true"
        :popup-style="{ 'top': '42px' }"
      />
    </van-popup>

    <van-popup v-model="billPicker" position="bottom" :duration="0.3">
      <van-picker title="支付方式" show-toolbar :columns="billList" value-key="sub_name" @confirm="submitBillway" @cancel="billPicker = false" />
    </van-popup>
    <div v-if="loadingMsg" style="border-radius:20px; width:200px;height:80px;background:#555;color:#fff; position:fixed;top:calc(50vh - 40px);left:calc(50vw - 100px);z-index:99999999;display:flex;justify-content:center;align-items:center;">
      <van-loading size="24px" color="#fff">{{loadingMsg}}...</van-loading>
    </div>
      <van-action-sheet v-model="showImageSelect" :actions="imageSelectActions" cancel-text="取消" description="选择添加图片的方式"
        close-on-click-action @select="onImageWaySelected" style="height: auto;" />
  </div>

</template>

<script>
import { Loading, NavBar,ActionSheet, ImagePreview, Form, Field, Search, Popup, Picker, Button, SwipeCell, Cell, Toast, Dialog, Icon, Row, Col, Divider } from 'vant'
import {
  AppSheetFeeOutSave,
  AppSheetFeeOutSubmit,
  AppSheetFeeOutLoad,
  SheetFeeOutRed,
  SheetFeeOutDelete,
  GetDisplaySheet, AppGetFeeOutDisplaySheetInfo
} from '../../api/api'
import globalVars from "../../static/global-vars";
import SelectCustomer from '../components/SelectCustomer'
import Position from '../../components/Position'
import TakePhoto from '../service/TakePhoto'
import ImageUtil from '../service/Image'
import AddFeeOut from '../FeeOut/AddFeeOut'
import Printing from "../Printing/Printing";
import FeeOutDisplayMoney from "./FeeOutDisplayMoney";
import SelectOne from "../components/SelectOne";
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import 'vue2-datepicker/locale/zh-cn';
export default {
  name: "FeeOut",
  data() {
    return {
      loadingMsg: '',
      isPrinting: false,
      isSubmitPopup: false,
      showCustomer: false,
      showPrepay: false,
      showBillWay: false,
      showFeeOut: false,
      IsSubmiting: false,
      billPicker: false,
      isRed: false,
      payway2: false,
      showImageSelect:false,
      imageSelectActions: [{ name: '拍照' }, { name: '相册' }],
      billList: [],
      billKey: 0,
      bPopupClientSelectDialog: false,
      bPopupSelectHappenTime:false,
      sheet: {
        sheet_id: '',
        sheet_no: '',
        make_time:'',
        approve_time: '',
        supcust_id: '',
        sup_name: '',
        total_amount: '',
        payway1_id: '',
        payway1_name: '',
        payway1_amount: '',
        payway2_id: '',
        payway2_name: '',
        payway2_amount: '',
        sheetRows: [],
        make_brief: '',
        appendixPhotos: []
      },
      displaySheets: [],
      showDisplay: false
    }
  },
  mounted() {
    console.log("sheet1", this.sheet)

    if (this.$route.query.sheetID) {
      this.sheet.sheet_id = this.$route.query.sheetID
    } else {
      this.sheet.sheet_id = ''
    }
    this.startNewPage()
  },
  components: {
    FeeOutDisplayMoney,
    "van-nav-bar": NavBar,
    "van-form": Form,
    "van-field": Field,
    "van-search": Search,
    "van-popup": Popup,
    "van-picker": Picker,
    "van-button": Button,
    "van-swipe-cell": SwipeCell,
    "van-cell": Cell,
    "van-icon": Icon,
    "van-row": Row,
    "van-col": Col,
    "van-divider": Divider,
    "van-action-sheet":ActionSheet,
    SelectCustomer,
    AddFeeOut,
    "select-one": SelectOne,
    "van-loading": Loading,
    DatePicker
  },
  computed: {
    canApprove() {
      return hasRight("money.sheetFeeOut.approve")
    },
    canMake() {
      return hasRight("money.sheetFeeOut.make")
    },

    canRed() {
      return hasRight("money.sheetFeeOut.red")
    },
    canAllowSheetArrears() {
      var allowSheetArrears = window.getRightValue('delicacy.allowSheetArrears.value')
      if (Number(this.sheet.left_amount) !== 0) {
        return true
      }
      //if(allowSheetArrears === 'true') {
      if (allowSheetArrears === 'false') {//默认允许欠款
        return false
      }
      return true
    },
    canGiveDisplayCrossDept() {
      let giveDisplayCrossDept = window.getRightValue('delicacy.giveDisplayCrossDept.value')
      return !(giveDisplayCrossDept === '' || giveDisplayCrossDept === 'false' || giveDisplayCrossDept === false);
    },
    canAppSelectSeller() {
      var appSelectSeller = window.getRightValue("delicacy.appSelectSeller.value");
      return appSelectSeller == "true"
    },

  },
  methods: {
    onInputHappenTime(){
      this.bPopupSelectHappenTime = true;
    },
    onImageWaySelected(item){
      switch (item.name) {
        case '拍照':
          ;
          this.takeAppendixPhotos(Camera.PictureSourceType.CAMERA)
          break;
        case '相册':
          ;
          // let fileDom = document.querySelector("#imgFile");
          // fileDom.click();
          this.takeAppendixPhotos(Camera.PictureSourceType.PHOTOLIBRARY)
          break;
    }
    },
    onSelectClient() {
      console.log(111)
      
      if (this.sheet.approve_time) return;
      this.bPopupClientSelectDialog = true;

    },
    remove_photo(idx) {
      console.log({ idx })
      this.sheet.appendixPhotos.splice(idx, 1)
    },
    preview_photo(idx) {
      ImagePreview({
        images: this.sheet.appendixPhotos,
        startPosition: idx,
        closeable: true,
        // asyncClose: true
      });
    },
    startNewPage() {
      let params = {
        sheetID: this.sheet.sheet_id
      }
      AppSheetFeeOutLoad(params).then(async res => {
        if (res.result === "OK") {
          this.billList = res.payways
          this.sheet = res.sheet
          this.sheet.supcust_id = this.$route.query.supcust_id ? this.$route.query.supcust_id : this.sheet.supcust_id
          this.sheet.sup_name = this.$route.query.sup_name ? this.$route.query.sup_name : this.sheet.sup_name
          if (!this.sheet.payway1_amount) {
            this.sheet.payway1_id = res.payways[0].sub_id
            this.sheet.payway1_name = res.payways[0].sub_name
            this.sheet.payway1_amount = ''
          }
          if (this.sheet.payway2_amount !== 0) {
            this.payway2 = true
          } else {
            this.sheet.payway2_id = res.payways[1].sub_id
            this.sheet.payway2_name = res.payways[1].sub_name
            this.sheet.payway2_amount = ''
          }
          this.sheet.make_brief = res.sheet.make_brief
          // this.sheet.appendixPhotos=this.sheet.appendix_photos?JSON.parse(this.sheet.appendix_photos).map(imgurl=>{
          //   return globalVars.obs_server_uri+"/uploads"+imgurl
          // }):JSON.parse("[]")
          var appendixOrigin = JSON.parse(this.sheet.appendix_photos ? this.sheet.appendix_photos : "[]")
          const obsUrls = appendixOrigin.filter(imgurl => !ImageUtil.isBase64(imgurl)).map(imgurl => { return globalVars.obs_server_uri + "/uploads" + imgurl })
          const originBase64Images = appendixOrigin.filter(imgurl => ImageUtil.isBase64(imgurl))
          this.sheet.appendixPhotos = []
          this.sheet.appendixPhotos = this.sheet.appendixPhotos.concat(originBase64Images)
          const waitTransformImages = ImageUtil.images2Base64(obsUrls)
          this.sheet.appendixPhotos = this.sheet.appendixPhotos.concat(await Promise.all(waitTransformImages))
        }
      })
      this.getDisplaySheet()
    },
    async takeAppendixPhotos(sourceType) {
      const originBase64 = await TakePhoto.takePhotos(sourceType)
      const compressBase64 = await ImageUtil.compress(originBase64)
      console.log("sheet2", this.sheet)
      this.sheet.appendixPhotos.push(compressBase64)
      this.$forceUpdate()
    },
    onClientSelected(client) {
      this.bPopupClientSelectDialog=false
      this.sheet.appendixPhotos = []
      if (this.sheet.sheetRows.length > 0) {
        Dialog.confirm({
          title: '是否继续？',
          message: '切换客户将删除陈列费',
          width:"320px"
        }).then(() => {
          this.sheet.supcust_id = client.ids
          this.sheet.sup_name = client.titles
          if(client.acct_cust_id){
            this.sheet.acct_supcust_id = client.acct_cust_id
            this.sheet.acct_supcust_name = client.acct_cust_name
          }
          else{
            this.sheet.acct_supcust_id =  this.sheet.supcust_id
            this.sheet.acct_supcust_name =  this.sheet.sup_name
          }
          this.showCustomer = client.isShow
          // 获取陈列费
          this.getDisplaySheet()
          this.handleChangeCustomer()
        })
      } else {
        this.sheet.supcust_id = client.ids
        this.sheet.sup_name = client.titles
        this.showCustomer = client.isShow
        // 获取陈列费
        this.getDisplaySheet()
      }
    },
    selectBillWay(key) {
      if (this.sheet.sheet_id !== '') return
      this.billPicker = true
      this.billKey = key
    },
    submitBillway(value) {
      this.billPicker = false
      if (this.billKey) {
        this.sheet.payway1_id = value.sub_id
        this.sheet.payway1_name = value.sub_name
      } else {
        this.sheet.payway2_id = value.sub_id
        this.sheet.payway2_name = value.sub_name
      }
    },
    addFeeout(objs) {
      this.showFeeOut = objs.isShow
      var sheet = this.sheet
      var supcust_id = sheet.supcust_id
      var left_amount = sheet.left_amount
      sheet.sheetRows.push(objs)
      sheet.total_amount += Number(objs.fee_sub_amount)
      if (supcust_id !== "") {
        sheet.payway1_amount = JSON.parse(JSON.stringify(sheet.total_amount - sheet.left_amount))
      } else {
        sheet.payway1_amount = JSON.parse(JSON.stringify(sheet.total_amount))
      }

    },
    deleteFeeOutRow(item, index) {
      Dialog.confirm({
        title: '删除记录',
        message: '确认要删除本条记录吗?',
        width:"320px"
      })
        .then(() => {
          this.sheet.sheetRows.splice(index, 1)
          this.sheet.total_amount = 0
          this.sheet.sheetRows.forEach(item => {
            this.sheet.total_amount += Number(item.fee_sub_amount)
          })
          this.sheet.left_amount = 0
          this.sheet.payway1_amount = JSON.parse(JSON.stringify(this.sheet.total_amount))

        })

    },
    firstPayway() {
      if (this.sheet.supcust_id == "") this.sheet.left_amount = "0"
      if (this.payway2) {
        this.sheet.payway2_amount = Number(this.sheet.total_amount) - Number(this.sheet.payway1_amount) - Number(this.sheet.left_amount)
      } else {
        if (this.sheet.supcust_id !== "") this.sheet.left_amount = Number(this.sheet.total_amount) - Number(this.sheet.payway1_amount)
        else this.payway1_amount = this.sheet.total_amount
      }
    },
    otherPayway() {
      if (this.sheet.supcust_id == "") this.sheet.left_amount = "0"
      this.sheet.payway1_amount = Number(this.sheet.total_amount) - Number(this.sheet.payway2_amount) - Number(this.sheet.left_amount)
    },
    onLeftAmountChange() {
      if (this.sheet.supcust_id !== "") {
        if (this.payway2) {
          this.sheet.payway1_amount = Number(this.sheet.total_amount) - Number(this.sheet.left_amount) - Number(this.sheet.payway2_amount)
        } else {
          this.sheet.payway1_amount = Number(this.sheet.total_amount) - Number(this.sheet.left_amount)
        }
      }
    },
    delCustomer() {
      this.sheet.supcust_id = ''
      this.sheet.sup_name = ''
      this.sheet.payway1_amount = Number(this.sheet.left_amount) + Number(this.sheet.payway1_amount)
      this.sheet.left_amount = 0
    },
    clearFeeOut() {
      Dialog.confirm({
        title: '清空单据',
        message: '请确认是否清空单据?',
        width:"320px"
      }).then(() => {
        this.sheet = {
          supcust_id: '',
          sup_name: '',
          sheetRows: [],
          total_amount: 0,
          payway1_id: '',
          payway1_name: '',
          payway1_amount: '',
          payway2_id: '',
          payway2_name: '',
          payway2_amount: '',
          make_brief: ''
        }
        Toast.success('已清空')
        this.isSubmitPopup = false
      })
    },
    goback() {
      myGoBack(this.$router)
    },
    outFeeOut() {
      this.isSubmitPopup = false
      myGoBack(this.$router)
    },
    getSheet() {
      let sheet = this.sheet
      sheet.payway1_amount = sheet.payway1_amount || 0
      sheet.payway2_amount = sheet.payway2_amount || 0
      sheet.operKey = this.$store.state.operKey;
      sheet.now_pay_amount = sheet.payway1_amount * 1 + sheet.payway2_amount * 1
      sheet.sheetType = 'ZC'
      sheet.visit_id = ""
      let visitRecord = this.$store.state.visitRecord
      if (JSON.stringify(visitRecord) !== "{}" && visitRecord.visit_id !== "") {
        if (sheet.supcust_id === visitRecord.shop_id) {
          sheet.visit_id = visitRecord.visit_id
        }
      }
      return sheet
    },
    btnSave_click() {
      let check = true
      let sheet = this.getSheet()
      var amount = Number(sheet.left_amount) + Number(sheet.payway1_amount) + Number(sheet.payway2_amount)
      isEmpty(amount, '支付金额不能为0')
      isEmpty(sheet.payway1_id, '请至少选择一种支付方式')
      isEmpty(sheet.sheetRows, '请添加费用清单')
      function isEmpty(value, info) {
        if (value === '' || Number(value) === 0 || value.length === 0) {
          Toast.fail(info)
          check = false
        }
      }
      if (check) {
        this.IsSubmiting = true;
        AppSheetFeeOutSave(sheet).then(res => {
          this.IsSubmiting = false;
          if (res.result === 'OK') {
            this.sheet.sheet_id = res.sheet_id
            this.sheet.sheet_no = res.sheet_no
            this.sheet.make_time = res.currentTime
            Toast.success('保存成功')
          } else {
            Toast.fail(res.msg)
          }
        })
          .catch((error) => {
            this.IsSubmiting = false;
            Toast.fail(error);
          });
      }
    },
    btnSubmit_click() {
      let check = true
      let sheet = this.getSheet()
      var amount = Number(sheet.left_amount) + Number(sheet.payway1_amount) + Number(sheet.payway2_amount)
      isEmpty(amount, '支付金额不能为0')
      isEmpty(sheet.payway1_id, '请至少选择一种支付方式')
      isEmpty(sheet.sheetRows, '请添加费用清单')
      function isEmpty(value, info) {
        if (value === '' || Number(value) === 0 || value.length === 0) {
          Toast.fail(info)
          check = false
        }
      }
      if (check) {
        this.IsSubmiting = true;
        AppSheetFeeOutSubmit(sheet).then(res => {
          this.IsSubmiting = false;
          if (res.result === 'OK') {
            this.sheet.sheet_id = res.sheet_id
            this.sheet.sheet_no = res.sheet_no
            this.sheet.approve_time = res.currentTime
            Toast.success('提交成功')
          } else {
            Toast.fail(res.msg)
          }
        })
          .catch((error) => {
            this.IsSubmiting = false;
            Toast.fail(error);
          });
      }
    },
    redBill() {
      Dialog.confirm({
        title: '红冲单据',
        message: '请确认是否红冲单据?',
        width:"320px"
      }).then(() => {
        let params = {
          operKey: this.$store.state.operKey,
          sheetID: this.sheet.sheet_id
        }
        SheetFeeOutRed(params).then(res => {
          if (res.result === "OK") {
            this.sheet.red_flag = '1'
            Toast.success('红冲成功')
          } else {
            Toast.fail('红冲失败')
          }
        })
      })
    },
    // 删除未审核单据
    btnDeleteSheet_click() {
      //sheet.sheet_id
      var that = this
      Dialog.confirm({
        title: '删除单据',
        message: '请确认是否删除',
        width:"320px"
      }).then(() => {
        var delFunc;
        delFunc = SheetFeeOutDelete;
        let params = {
          operKey: this.$store.state.operKey,
          sheet_id: this.sheet.sheet_id,
        };
        delFunc(params).then((res) => {
          if (res.result === "OK") {
            Toast.success("删除成功,即将退出该页面");
            setTimeout(function () {
              that.outFeeOut()
            }, 1000);
          } else {
            Toast.fail("删除失败:" + res.msg);
          }
        });

        console.log(this.sheet.sheet_id)
      })
    },
    handlePrintSheet() {
      if (this.sheet.sheetRows.length <= 0) {
        Toast.fail("请添加费用");
        return
      } else {
        this.isPrinting = true
        var imageBase64 = "";
        if (this.$store.state.company_cachet) {
          imageBase64 = "data:image/png;base64," + this.$store.state.company_cachet;
        }
        var that = this
        this.loadingMsg = '正在打印'
        let sheetTemp = JSON.parse(JSON.stringify(this.sheet))
        Printing.printFeeOut(sheetTemp, imageBase64,
          res => {
            that.loadingMsg = ''
            that.isPrinting = false
            if (res.result == "OK") {
              Toast.success("打印成功");
            } else {
              Toast.fail(res.msg);
            }
          }
        );
      }
    },
    handleShowDisplay() {
      this.showDisplay = true
      this.getDisplaySheet()
    },
    handleClickCancelDisplay() {
      this.showDisplay = false
    },
    handlePayWay(showFlag) {
      this.payway2 = showFlag
      if (!showFlag) {
        this.sheet.total_amount = 0
        this.sheet.sheetRows.forEach(item => {
          this.sheet.total_amount += Number(item.fee_sub_amount)
        })
        this.sheet.payway1_amount = JSON.parse(JSON.stringify(this.sheet.total_amount))
        this.sheet.payway2_amount = ''
        this.sheet.payway2_id = ''
        this.sheet.payway2_name = ''
      }
    },
    handleShowCustomer() {
      this.showCustomer = true
    },
    handleChangeCustomer() {
      if (this.sheet.approve_time !== '') return
      this.sheet.sheetRows = this.sheet.sheetRows.filter(item => {
        return item?.display_id === undefined
      })
      this.sheet.total_amount = 0
      this.sheet.sheetRows.forEach(item => {
        this.sheet.total_amount += Number(item.fee_sub_amount)
      })
      this.sheet.left_amount = 0
      this.sheet.payway1_amount = JSON.parse(JSON.stringify(this.sheet.total_amount))
      this.handlePayWay(false)
    },
    // 陈列费用相关
    getDisplaySheet() { // 获取陈列费用
      if (this.sheet.supcust_id === "") {
        this.displaySheets = []
        return
      }
      AppGetFeeOutDisplaySheetInfo({
        operKey: this.$store.state.operKey,
        supcustId: this.sheet.supcust_id,
        deptPath: this.$store.state.operInfo.oper_dept_path,
        canGiveDisplayCrossDept: this.canGiveDisplayCrossDept
      }).then(res => {
        if (res.code === 0) {
          this.displaySheets = res.result
          this.handleDisplaySheetInfo()
        }
      }).catch(err => {
        console.log(err)
      })

    },
    // 处理陈列费用结构
    handleDisplaySheetInfo() {
      let that = this
      this.displaySheets.forEach(displayItem => {
        this.$set(displayItem, 'display_given_month_info', [])
        const startYear = Number(displayItem.start_time.substring(0, 4));
        const startMonth = Number(displayItem.start_time.substring(5, 7));
        const endYear = Number(displayItem.end_time.substring(0, 4));
        const endMonth = Number(displayItem.end_time.substring(5, 7));
        if (displayItem.disp_template_id) {
          if (displayItem.maintain_arr !== '') {
            displayItem.maintain_arr = JSON.parse(displayItem.maintain_arr)
          } else {
            displayItem.maintain_arr = []
          }
        }
        if (startYear === endYear) {  // 同年的
          let index = 0;
          for (let i = startMonth; i <= endMonth; i++) {
            handleDisplayitem(displayItem, startYear, i, () => {
              index++
            }, index)
          }
        } else {  // 跨年
          let index = 0;
          for (let i = startMonth; i <= 12; i++) {
            handleDisplayitem(displayItem, startYear, i, () => {
              index++
            }, index)
          }
          for (let i = 1; i <= endMonth; i++) {
            handleDisplayitem(displayItem, endYear, i, () => {
              index++
            }, index)
          }
        }
      })
      function handleDisplayitem(displayItem, year, month, indexCallBack, index) {
        let display_given_month_info_item = {
          display_month: '',
          display_month_index: '',
          display_month_qty: '',
          display_month_given: 0,
          display_month_fee_sub_amount: '',
          display_month_remark: '',
          give_condition: '',
          maintain_month_times: 0,
          month_maintain_times: 0, // 月维护次数
          can_edit: true  // 能够选中进行编辑
        }
        display_given_month_info_item = JSON.parse(JSON.stringify(display_given_month_info_item))
        var monthResult = (month <= 9 ? '0' + month : month)
        display_given_month_info_item.display_month = `${year}-${monthResult}`
        indexCallBack()
        display_given_month_info_item.display_month_index = index + 1
        display_given_month_info_item.display_month_qty = Number(displayItem[`month${index + 1}_qty`])
        display_given_month_info_item.display_month_given = Number(displayItem[`month${index + 1}_given`])
        display_given_month_info_item.display_month_fee_sub_amount = ''
        display_given_month_info_item.give_condition = displayItem.give_condition
        display_given_month_info_item.month_maintain_times = displayItem.month_maintain_times
        console.log('can_edit', display_given_month_info_item.can_edit)
        if (displayItem.disp_template_id) {
          const displayItemMaintainItem = displayItem.maintain_arr.find(maintainItem => maintainItem.maintain_month === display_given_month_info_item.display_month)
          if (displayItemMaintainItem) {
            display_given_month_info_item.maintain_month_times = displayItemMaintainItem.maintain_times
          } else {
            display_given_month_info_item.maintain_month_times = 0
          }
        }
        if (display_given_month_info_item.display_month_qty === display_given_month_info_item.display_month_given) {
          display_given_month_info_item.can_edit = false
        } else {
          const allowAdvanceDisplayFee = window.getRightValue('delicacy.allowAdvanceDisplayFee.value').toLowerCase() === "true"
          if (allowAdvanceDisplayFee) {
            if (displayItem.disp_template_id) {
              if (display_given_month_info_item.give_condition === 'after_maintain') {
                display_given_month_info_item.can_edit = Number(display_given_month_info_item.maintain_month_times) === Number(display_given_month_info_item.month_maintain_times)
              } else if (display_given_month_info_item.give_condition === 'after_sign') {
                display_given_month_info_item.can_edit = true
              }
            }
          } else {
            // 判断月份
            let displayYear = Number(display_given_month_info_item.display_month.slice(0, 4))
            let displayMonth = Number(display_given_month_info_item.display_month.slice(-2))
            let nowYear = Number(new Date().getFullYear())
            let nowMonth = Number(new Date().getMonth() + 1)
            if (nowYear === displayYear) {
              if (displayMonth <= nowMonth) {
                display_given_month_info_item.can_edit = true
              } else {
                display_given_month_info_item.can_edit = false
              }
            } else if (nowYear > displayYear) {
              display_given_month_info_item.can_edit = true
            } else if (nowYear < displayYear) {
              display_given_month_info_item.can_edit = false
            }
          }

          if (displayItem.responsible_worker) {
            if (that.$store.state.operInfo.oper_id !== displayItem.responsible_worker && !that.canGiveDisplayCrossDept) {
              display_given_month_info_item.can_edit = false
            }
          } else {
            if (that.$store.state.operInfo.oper_id !== displayItem.seller_id && !that.canGiveDisplayCrossDept) {
              display_given_month_info_item.can_edit = false
            }
          }
        }

        if (display_given_month_info_item.display_month_qty > 0) {
          displayItem.display_given_month_info.push(display_given_month_info_item)
        }
      }
    },
    // 确认陈列费
    handleConfirmClickDisplay() {
      console.log('handleConfirmClickDisplay')
      let showMsgFlag = false
      this.displaySheets.forEach(displaySheet => {
        displaySheet.display_given_month_info.forEach(monthItem => {
          showMsgFlag = false
          if (Number(monthItem.display_month_fee_sub_amount) !== 0) {
            const displayMonth = monthItem.display_month + '-01'
            let objs = {
              fee_sub_amount: Number(monthItem.display_month_fee_sub_amount),
              fee_sub_id: displaySheet.fee_sub_id,
              fee_sub_name: displaySheet.sub_name,
              display_id: displaySheet.sheet_id,
              display_no: displaySheet.sheet_no,
              display_month: new Date(displayMonth.myReplace('-', '/')).format("yyyy-MM-dd hh:mm:ss"),
              remark: monthItem.display_month_remark + `${monthItem.display_month}月陈列费` + (displaySheet.disp_template_id ? displaySheet.disp_template_name : ''),
            }
            let tempItem = this.sheet.sheetRows.find(sheetRow => objs.display_id === sheetRow.display_id && objs.display_month === sheetRow.display_month)
            if (tempItem) {
              showMsgFlag = true
              if (Number(tempItem.fee_sub_amount) + Number(objs.fee_sub_amount) <= (Number(monthItem.display_month_qty) - Number(monthItem.display_month_given))) {
                tempItem.fee_sub_amount = Number(tempItem.fee_sub_amount) + Number(objs.fee_sub_amount)
              } else {
                tempItem.fee_sub_amount = Number(monthItem.display_month_qty) - Number(monthItem.display_month_given)
              }
              if (tempItem.remark === '') {
                tempItem.remark = objs.remark
              } else {
                if (objs.remark !== '') {
                  tempItem.remark = tempItem.remark + '，' + objs.remark
                }
              }
            } else {
              this.sheet.sheetRows.push(JSON.parse(JSON.stringify(objs)))
            }
            this.sheet.total_amount = 0
            this.sheet.sheetRows.forEach(item => {
              this.sheet.total_amount += Number(item.fee_sub_amount)
            })
            this.sheet.payway1_amount = JSON.parse(JSON.stringify(this.sheet.total_amount))
            if (showMsgFlag) {
              Toast('存在相同陈列费，将进行合并')
            }
          }
        })
      })
      this.showDisplay = false
      console.log('handleConfirmClickDisplay', this.sheet)
    },
  }
}
</script>

<style lang="less" scoped>
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_acent_jb: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_acent_je: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_acent_js: {
  display: flex;
  align-items: center;
  justify-content: flex-start;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
/deep/.van-cell {
  font-size: 15px;
}
/deep/.van-nav-bar__text {
  color: #ffffff;
}
/deep/.van-cell__title {
  text-align: left;
}
/deep/.van-swipe-cell__right {
  vertical-align: top;
  button {
    vertical-align: top;
    height: 100%;
  }
}
// /deep/.van-popup--right {
//   top: 46px;
//   -webkit-transform: translate3d(0, 0, 0);
//   transform: translate3d(0, 0, 0);
// }
// /deep/.van-overlay {
//   height: calc(100% - 46px) !important;
//   top: 46px !important;
// }
.report_no_box {
  width: 100%;
  height: 100%;
  padding-top: 165px;
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 14px;
  }
}
.alloc_list {
  padding: 5px 8px;
  h4 {
    height: 40px;
    font-size: 16px;
    border: 1px solid #cccccc;
    margin-bottom: 10px;
    @flex_a_j();
  }
}
.noSubmitStyle {
  input {
    border: none;
    background: #555555;
  }
}
.billNameStyle {
  display: flex;
  align-items: center;
}
.prepay_title {
  font-size: 14px;
  background: #ffffff;
  height: 20px;
  @flex_acent_jb();
  padding: 5px 12px;
}
.accountUnit {
  font-size: 14px;
  height: 20px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 5px 12px;
}
.public_box2_b {
  height: calc(100% - 109px);
  .sheet_state {
    z-index: 999;
    position: fixed;
  }
  .approved {
    width: 105px;
    height: 60px;
    top: 80px;
    left: 80px;
  }
  .reded {
    width: 86px;
    height: 75px;
    top: 80px;
    left: 70px;
  }
}
.cost_list {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: calc(60vh - 20px);
}
.cost_footer {
  width: 100%;
  overflow: hidden;
  background-color: #ffffff;
  position: absolute;
  left: 0;
  bottom: 0;
  border-top: 1px solid #f2f2f2;
  box-shadow: 0 -2px 5px #f2f6fc;

  .cost_footer_t {
    height: 25px;
    font-size: 15px;
    border-bottom: 1px solid #f2f2f2;
    @flex_acent_jb();
    padding: 5px 10px;
    box-sizing: border-box;
  }
  .cost_footer_b {
    height: 60px;
    @flex_acent_je();
    vertical-align: top;
    padding: 1px 3px;
    .button {
      height: 100%;
      vertical-align: top;
      margin: 5px;
    }
  }
}
.other_popup {
  overflow: hidden;
  .custom_h5 {
    height: 46px;
    line-height: 46px;
    font-size: 16px;
    color: steelblue;
    background: #f2f2f2;
    position: relative;
    .icon_h5 {
      position: absolute;
      height: 46px;
      width: 46px;
      right: 0;
      top: 0;
      display: block;
      @flex_a_j();
      font-size: 20px;
    }
  }
  .other_operate {
    width: 100%;
    height: calc(100% - 46px);
    .van-divider {
      margin: 10px 0 3px 0;
    }
    .other_operate_total {
      height: 30px;
      font-size: 16px;
      @flex_acent_js();
      padding: 0 10px;
      margin-left: 7px;
    }
    .payway {
      margin: 20px 0;
      .payway {
        height: 35px;
        font-size: 15px;
        @flex_a_j();
        margin: 5px;
        .van-col {
          height: inherit;
          @flex_a_j();
          color: #000000;
        }
        .van-col:first-child {
          background: #fff;
          border-radius: 10px;
          border: 1px solid #ccc;
          margin: 0 8px;
        }
        input {
          width: 100%;
          height: 100%;
          border: none;
          outline: none;
          border-bottom: 1px solid #ccc;
          text-align: center;
        }
        .payway_add {
          font-size: 20px;
          color: #ccc;
          margin-left: 10px;
        }
      }
    }
    .other_operate_content {
      height: 40px;
      vertical-align: top;
      margin: 10px 0;
      @flex_a_j();
      button {
        width: 100px;
        height: 100%;
        vertical-align: top;
        margin: 0 15px;
      }
    }
  }
}
.selectedRed {
  background: #f2f2f2;
  border: 1px solid #f2f2f2;
}
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};

.public_query_titleSrc {
  padding: 10px 15px 3px 15px;

  display: flex;
  flex-direction: column;
  background: #ffffff;

  .public_query_wrapper {
    margin-top: 5px;
    
    .public_query_titleSrc_item {
      position: relative;
      vertical-align: top;
      border: none;
      padding: 10px 0 3px 0;

      div {
        height: 100%;
        width: calc(100% - 40px);
        padding: 0 30px 0 10px;
        border: none;
        font-size: 15px;
        line-height: 35px;
        color: #333333;
        text-align: left;
      }

      input {
        height: 100%;
        padding: 0 10px 4px 0px;
        border: none;
        font-size: 15px;
        line-height: 35px;
        color: #333333;
        vertical-align: top;
        border-bottom: 1px solid #eee;
        text-align: right;
      }

      .van-icon {
        position: absolute;
        left: 5px;
        top: 0;
        bottom: -10px;
        width: 30px;
        text-align: center;
        font-size: 22px;
        @flex_a_j();
        color: #aaa;
        background-color: #ffffff;
      }
    } 
  }
} 
/deep/ .mx-datepicker {
  width: 100%;
}

/deep/ .mx-calendar {
  width: 100%;
}
.display-wrapper {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  text-align: left;
  .display-content {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    min-height: 40px;
    border-bottom: 1px solid #efefef;
    flex-direction: column;
    padding: 10px 20px;
    width: 100%;
    .display-content-name-amount {
      box-sizing: border-box;
      display: flex;
      width: 100%;
      justify-content: space-between;
      .display-content-name,
      .display-content-amount {
        flex: 1;
        font-size: 20px;
        text-align: left;
      }
      .display-content-amount {
        text-align: right;
      }
    }
    .display-content-display_no,
    .display-content-remark {
      width: 100%;
      font-size: 14px;
      color: #a7a7a7;
    }
  }
}
.display-btns {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  box-sizing: border-box;
  .btn_del {
    display: flex;
    color: #000;
    align-items: center;
    justify-content: center;
    background-color: #fde3e4;
    border-radius: 10px;
    width: 40%;
    height: 40px;
  }
}
.photo {
  width: 60px;
  height: 60px;
  margin-right: 6px;
}
.appendix-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}
.appendix_tip {
  font-size: 12px;
  color: #666;
}
.other_operate_content_left {
  display: flex;
  width: 70%;
  left: 0px;
  flex-direction: row;
  overflow-x: scroll;
  position: absolute;
}
.other_operate_content_left::-webkit-scrollbar {
  display: none;
}
.other_operate_content_right {
  width: 80px;
  position: fixed;

  right: 0px;
}
.appendixphoto-container {
  margin-left: -10px;
  border-radius: 50%;
  width: 60px;
  margin-left: 10px;
  height: 60px;
  position: relative;
  .remove-icon {
    position: absolute;
    top: -4px;
    margin-left: 52px;
    color: #f31010;
    font-size: 16px;
    float: right;
  }
}
.iconfont {
  width: 40px;
  height: 40px;
  padding: 10px;
  font-size: 36px;
  color: #ccc;
  border: 1px solid #ccc;
}
</style>
