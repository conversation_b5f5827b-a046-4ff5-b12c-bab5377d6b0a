<template>
  <div class="pages">
    <van-nav-bar :title="seller_name" left-arrow @click-left="myGoBack($router)" safe-area-inset-top>
      <template #right >
        <div class="check_date_type" v-if="!sheets.sheet_id" @click="popHappenTime">交账日期<van-icon name="clock-o" /></div>
      </template>
    </van-nav-bar>

    <div class="public_box2" >
      <div v-if="sheets.sheet_id" class="date_t" style="padding-right:0">对账日期: {{printStartAndEndTime.start_time}} ~ {{printStartAndEndTime.end_time}}</div>
      <div v-if="sheets.sheet_id" class="date_t">交账日期: {{sheets.happen_time}}</div>
      <div v-if="!sheets.sheet_id" class="date_t">对账日期: {{this.start_time}} ~ {{this.end_time}}</div>
      <div v-if="!sheets.sheet_id" class="date_t">交账日期:
        <template v-if="!happenTimeShow">{{happen_time_show}}</template>
        <template v-else>选择交账日期</template>
        </div>
      <div v-if="sheets.sheet_id " >
        <span>当日有{{dayCheckSheetsID.split(',').length}}张交账单</span>
        <button style="margin-left: 20px;background-color: #fff;border: 1px solid #aaa;border-radius: 5px"
                v-show="!seller_name.includes(this.isAllSheet) && dayCheckSheetsID.split(',').length >= 2" @click="showAllCheckSheets">查看汇总</button>



      </div>
      <van-tabs v-model="curTab" swipeable :style="{height:(sheets.sheet_id?'calc(100% - 120px)':'calc(100% - 120px)')}">
        <van-tab title="汇总"><account-summary ref="summaryView" :sheets="sheets"></account-summary></van-tab>
        <van-tab title="明细"><account-detail ref="sheetsView"  :sheets="sheets"></account-detail></van-tab>
        <van-tab title="商品"><account-item ref="itemsView" :sheets="sheets"></account-item></van-tab>
        <van-tab title="单据明细"><account-sheet-detail ref="accountSaleDetail" :sheets="sheets"></account-sheet-detail></van-tab>
      </van-tabs>
    </div>
    <div class="footer">
      <van-button v-if="canApprove && !this.sheets.sheet_id" color="#F56C6C" class="t_footer" @click="onSubmitCheckAccount">交账</van-button>
      <van-button v-else-if="canRed && this.sheets.sheet_id && !this.sheets.isRed" color="#F56C6C" class="t_footer" @click="redCheckSheet">红冲</van-button>
      <van-button v-else-if="this.sheets.isRed" type="danger" color="#F56C6C" disabled plain class="t_footer">已红冲</van-button>
<!--      <van-button style="background-color:#fafafa;margin-left:20px"  class="t_footer"  @click="btnPrint_click" >{{curTab==0?'打印汇总':curTab==1?'打印单据':curTab==2?'打印商品':''}}</van-button>-->
      <van-button style="background-color:#fafafa;margin-left:20px"  class="t_footer"  @click="btnPrint_click" >打印</van-button>
    </div>
    <van-popup
    v-model="happenTimeShow"
    round
    position="bottom"
    @click-overlay="cancelHappenTime"
    :style="{ height: '50%' }"
  >
    <van-datetime-picker

    v-model="happen_time"
    @confirm="confirmHappenTime"
    @change="changeHappenTime"
    @cancel="cancelHappenTime"
    type="date"
    title="选择年月日"/>
  </van-popup>
    <Dialog
        v-model="printDialogShow"
        title="打印交账单"
        width="320px"
        @confirm="printConfirm"
        @cancel="printCancel"
        show-cancel-button>
        <van-checkbox-group v-model="printCheckAccountDialogResult"    ref="checkboxGroup" class="checkbox-group" direction="horizontal">
          <van-checkbox name="summery" checked-color="#f66" shape="square">汇 总</van-checkbox>
          <van-checkbox name="detail" checked-color="#f66" shape="square">明 细</van-checkbox>
          <van-checkbox name="item" checked-color="#f66" shape="square">商 品</van-checkbox>
          <van-checkbox name="saleDetail" v-if="printCheckAccountDialogResult.indexOf('detail')!=-1" checked-color="#f66" shape="square">销售明细</van-checkbox>
          <div v-if="printCheckAccountDialogResult.indexOf('item')!=-1" class="item-sub-options">
            <div class="sub-option-title">选择商品类型:</div>
            <van-checkbox name="sale" checked-color="#f66" shape="square">销 售</van-checkbox>
            <van-checkbox name="return" checked-color="#f66" shape="square">退 货</van-checkbox>
            <van-checkbox name="free" checked-color="#f66" shape="square">赠 品</van-checkbox>
          </div>
        </van-checkbox-group>
    </Dialog>
  </div>
</template>
<script>
import { NavBar, Tab, Tabs, Button,Icon,Toast, Dialog,DatetimePicker,Popup,Checkbox, CheckboxGroup } from "vant";
import { LoadCheckSheet, NewCheckSheet,SubmitCheckAccount, SheetCheckRed} from '../../api/api';
import store from '../../store/store';
import AccountSummary from "./AccountSummary";
import AccountDetail from "./AccountDetail";
import AccountItem from "./AccountItem";
import Printing from "../Printing/Printing";
import AccountSheetDetail from './AccountSheetDetail.vue';
export default {
  name:'ShowAccount',
  data() {
    return {
      pageSize: 1000,
      startRow: 0,
      dateType: '',
      seller_name: '',
      curTab: 0,
      listData: [],
      supList: [],
      sheets:{},
      detailData:[],
      queryData:{},
      maker_id:'',
      onloadItems:{},
      onloadItemsData:{},
      onLoadOverviewData:{},
      onLoadOverview:{},
      sonUpDatas:{},
     // hasMounted:false,
      start_time: '',
      end_time: '',
      happen_time: this.dateFormat("YYYY-mm-dd HH:MM:SS", new Date()),
      happen_time_show: this.dateFormat("YYYY-mm-dd HH:MM:SS", new Date()),
      happenTimeShow:false,
      showPrintSaleSheetCheckBox:false,
      printStartAndEndTime: {
        start_time: '',
        end_time: ''
      },
      printDialogShow:false,
      printCheckAccountDialogResult:[],
      dayCheckSheetsID:'',
      isAutoAppendItemTypes: true,
      isAllSheet: '日汇总'
    };
  },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        //因为当钩子执行前，组件实例还没被创建
        // vm 就是当前组件的实例相当于上面的 this，所以在 next 方法里你就可以把 vm 当 this 来用了。
        var sheets = to.query.sheets
        if(from.name == "SaleSheet") {
          if(sheets){
            vm.sheets.saleSheets = sheets
          }
          // redFlag = to.query.redFlag
          // sheetID = from.query.sheetID
          // if(redFlag !=""){
          //   for(var tempi = 0 ;tempi<vm.sheets.saleSheets.length;tempi++){
          //     if(sheetID === vm.sheets.saleSheets[tempi].sheet_id){
          //         vm.sheets.saleSheets.splice(tempi,1)
          //     }
          //   }
          // }
        }
        // if(from.name == "Login" && to.name == 'Workbench') {
        //   vm.getAppDesktop();
        // }
      });
  },
  beforeRouteLeave (to, from, next) {
    var sheets = ""
    if(to.name =="SaleSheet"){
      sheets = this.sheets.saleSheets
    }
    to.query.sheets = sheets
    next()
    // ...
  },
  mounted() {
    // 从路由中获取传值
    this.printDialogShow = false
    this.queryData = this.$route.query
    this.maker_id = this.queryData.maker_id
    if (isiOS) {
      this.start_time = this.start_time.replace(/-/g, '/')
      this.end_time = this.end_time.replace(/-/g, '/')
    }
    this.onloadData()
    this.printCheckAccountDialogResult = this.printResult
    //this.hasMounted=true
  },
  activated() {
    this.printDialogShow = false
    // if(!this.hasMounted && this.$route.query.sellerID !== undefined){//mounted有时不会触发，如果没触发，在activated里执行，这可能是vue bug // 其他页面没有query信息携带回来，导致保存的时候getterid未保存
    //   this.queryData = this.$route.query
    //   this.onloadData()
    // }
    // this.hasMounted = false
  },
  components: {
    "van-nav-bar": NavBar,
    "van-icon":Icon,
    "van-tabs": Tabs,
    "van-tab": Tab,
    "van-datetime-picker":DatetimePicker,
    "van-button": Button,
    "van-popup":Popup,
    "account-summary": AccountSummary,
    "account-detail": AccountDetail,
    "account-item": AccountItem,
    Dialog: Dialog.Component,
    'van-checkbox':Checkbox,
    "van-checkbox-group":CheckboxGroup,
    AccountSheetDetail
  },
  computed:{
    canApprove(){
       return hasRight('sale.sheetCheckSheets.approve')
    },
    canRed(){
       return hasRight('sale.sheetCheckSheets.red')
    },
    printResult() {
      return this.$store.state.printCheckAccountDialogResult
    }


  },
  watch: {
    printDialogShow(val) {
      if (val) {
        const saved = localStorage.getItem('printCheckPrefs');
        console.log("printCheckPrefs11111",localStorage.getItem('printCheckPrefs'));
        if (saved) {
          this.printCheckAccountDialogResult = JSON.parse(saved);
        } else {
          this.printCheckAccountDialogResult = ['summery', 'detail', 'item', 'saleDetail', 'sale', 'return', 'free'];
        }
      }
    },
    printCheckAccountDialogResult: {
      handler(newVal) {
        if (newVal) {
          if (!newVal.includes('detail') && newVal.includes('saleDetail')) {
            this.$nextTick(() => {
              this.printCheckAccountDialogResult = newVal.filter(item => item !== 'saleDetail');
            });
          }
          const itemTypes = ['sale', 'return', 'free'];

        if (newVal.includes('item')&& this.isAutoAppendItemTypes) {
            const missingTypes = itemTypes.filter(type => !newVal.includes(type));
            if (missingTypes.length > 0) {
              this.$nextTick(() => {
                this.printCheckAccountDialogResult = [...newVal, ...missingTypes];
                this.isAutoAppendItemTypes = false; 
              });
            } else {
            this.isAutoAppendItemTypes = false;
          }
        }
        if (!newVal.includes('item')) {
            const hasItemTypes = itemTypes.some(type => newVal.includes(type));
            if (hasItemTypes) {
              this.$nextTick(() => {
              this.printCheckAccountDialogResult = newVal.filter(item => !itemTypes.includes(item));
              this.isAutoAppendItemTypes = true; 
            });
          } else {
            this.isAutoAppendItemTypes = true;
          }
        }
      }
    },
      deep: true
    }
  },
  methods: {
    // 初始化加载数据
    computeDate(date, days) {
        var d = new Date(date);
        d.setDate(d.getDate() + days);    //如果加月就是d.getMonth(); 以此类推
        var m = d.getMonth() + 1;
        return d.getFullYear() + '-' + m + '-' + d.getDate();// + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();  //返回格式 2016-4-27 13:18:00 （可根据自己需要自行设定返回内容）
    },
    onloadData() {
      var startTime='',endTime=''
      if(!this.queryData.sheet_id){
        this.dateType = this.queryData.dateType
        var dateTypeID=this.queryData.dateTypeIds
        this.seller_name = this.queryData.sellerName+'交账详情'

        if(dateTypeID=='spec_date'){
          startTime=this.queryData.startDate
          endTime=this.queryData.endDate +' 23:59:59'
        }
        else if(dateTypeID=='yesterday'){
          startTime=this.computeDate(new Date(),-1)
          endTime=this.computeDate(new Date(),-1)+' 23:59:59'
        }else if(dateTypeID=='beforeYesterday'){
          startTime=this.computeDate(new Date(),-2)
          endTime=this.computeDate(new Date(),-2)+' 23:59:59'
        }else if(this.dateTypeIds=='alldays'){
          startTime = ''
          endTime = ''
        } else
        {
           var nDay=dateTypeID.replace('days','')-1
           startTime=this.computeDate(new Date(),-nDay)
           endTime=this.computeDate(new Date(),0)+' 23:59:59'
        }
      }else{
        this.sheets.sheet_id = this.queryData.sheet_id
        this.sheets.happen_time = this.queryData.happen_time
        console.log(this.sheets.happen_time)
        this.seller_name = this.queryData.sellerName+'交账详情'
      }
      let params
      let LoadFunc
      let checked
      this.start_time = startTime
      this.end_time = endTime
      this.printStartAndEndTime.start_time =  this.start_time
      this.printStartAndEndTime.end_time = this.end_time
      if(this.sheets.sheet_id){
        this.seller_name = this.queryData.sellerName+'交账详情'
        params = {
          sheetID: this.sheets.sheet_id,
          startTime:startTime,
          endTime:endTime,
          happenTime: this.queryData.happen_time||this.sheets.happen_time,
          getterID: this.queryData.maker_id || this.maker_id
        }
        LoadFunc = LoadCheckSheet
        checked = true
      }else{
        params = {
          sellerID: this.queryData.sellerID,
          sellerName:this.queryData.sellerName,
          pageSize: this.pageSize,
          startRow: this.startRow,
       // dateType: this.queryData.dateTypeIds? this.queryData.dateTypeIds:''
          startTime:startTime,endTime:endTime
        }
        LoadFunc = NewCheckSheet
        checked = true
      }
      LoadFunc(params).then(res => {
        if(res.result === 'OK') {
          console.log(res)
          var count = 0;
          this.dayCheckSheetsID = res.dayCheckSheetsID
          updateSheetsCheck(res.saleSheets, checked, '')
          updateSheetsCheck(res.borrowSheets, checked, '')
          updateSheetsCheck(res.prepaySheets, checked, 'prepaySheets')
          updateSheetsCheck(res.pregetSheets, checked, '')
          updateSheetsCheck(res.getArrearSheets, checked, 'getArrearSheets')
          updateSheetsCheck(res.feeOutSheets, checked, '')
          updateSheetsCheck(res.incomeSheets, checked, '')
          
          
          this.sheets = {...res,
                      sheet_id:this.sheets.sheet_id,
                      happen_time: this.sheets.happen_time,
                      isRed: false}
          console.log(this.sheets)
          if(this.sheets.sheet_id) {
            //
            this.printStartAndEndTime.start_time = res.printTitle.start_time
            this.printStartAndEndTime.end_time = res.printTitle.end_time
          }
          this.$store.commit('checkedSheetsCount', -this.$store.state.checkedSheetsCount)
          this.$store.commit('checkedSheetsCount', count)
          console.log('checked Num',this.$store.state.checkedSheetsCount)

          function updateSheetsCheck(sheets, check, sheetFlag){
            if(!sheets)
              return

            let moneyInoutFlag = 1
            if(sheetFlag === 'prepaySheets') {
              moneyInoutFlag *= -1
            }
            sheets.forEach(function(sheet){
              if (sheetFlag === 'getArrearSheets' || sheetFlag === 'prepaySheets') {
                sheet.prepay_amount = 0
                sheet.zc_amount = 0
                sheet.qtsr_amount = 0


                if (sheet.payway1_type === 'YS') {
                  sheet.prepay_amount += Number(sheet.payway1_amount)
                } else if (sheet.payway1_type === 'ZC') {
                  sheet.zc_amount += Number(sheet.payway1_amount)
                } else if (sheet.payway1_type === 'QTSR') {
                  sheet.qtsr_amount += Number(sheet.payway1_amount)*moneyInoutFlag
                }

                if (sheet.payway2_type === 'YS') {
                  sheet.prepay_amount += Number(sheet.payway2_amount)
                } else if (sheet.payway2_type === 'ZC') {
                  sheet.zc_amount += Number(sheet.payway2_amount)
                } else if (sheet.payway2_type === 'QTSR') {
                  sheet.qtsr_amount += Number(sheet.payway2_amount)*moneyInoutFlag
                }
                if (sheet.payway3_type === 'YS') {
                  sheet.prepay_amount += Number(sheet.payway3_amount)
                } else if (sheet.payway3_type === 'ZC') {
                  sheet.zc_amount += Number(sheet.payway3_amount)
                } else if (sheet.payway3_type === 'QTSR') {
                  sheet.qtsr_amount += Number(sheet.payway3_amount)*moneyInoutFlag
                }


                if(sheetFlag === 'getArrearSheets') {
                  sheet.real_get_amount = Number(sheet.real_get_amount) -  Number(sheet.prepay_amount) -  Number(sheet.zc_amount) -  Number(sheet.qtsr_amount)
                }
                if(sheetFlag === 'prepaySheets') {
                  sheet.real_get_amount = Number(sheet.real_get_amount) -  Number(sheet.left_amount) -  Number(sheet.disc_amount) -  Number(sheet.qtsr_amount)
                }
              }
                sheet.isChecked=check
                if(sheet.isChecked)
                  count++
            })
          }
        }
      })
    },
    // 获取明细中传来的选中值
    onSheetCheck(keys){
      let itemObjs = {
        isActive:keys,
        isType:1,
        datasAttr:this.onloadItemsData
      }
      this.onloadItems = itemObjs;
      this.onLoadOverview = itemObjs
    },
    // 获取汇总中传来的值
    onSumDone(obj){
      this.sonUpDatas = obj
    },
    btnPrint_click(){
      if(this.$store.state.checkedSheetsCount != 0 || this.sheets.sheet_id){
        // 弹框
        this.printDialogShow = true
        //1. 用户选择并确认
        //2. 调用各组件信息，进行对象结构，抽取公共信息
        //3. 调用打印函数

        // if(this.curTab==0){
        //   this.$refs.summaryView.print(this.queryData.sellerName,this.printStartAndEndTime)
        // }
        // else if(this.curTab==1){
        //   this.$refs.sheetsView.print(this.queryData.sellerName,this.printStartAndEndTime)
        // }
        // else if(this.curTab==2){
        //   this.$refs.itemsView.print(this.queryData.sellerName,this.printStartAndEndTime)
        // }
      }else{
        Toast.fail('未选择单据')
      }
    },
    printfAcccount() {
      //console.log(this.printCheckAccountDialogResult);
      this.$store.commit("printCheckAccountDialogResult",this.printCheckAccountDialogResult)
      let sheet = {}
      let summery = {}
      let detail = {}
      let item = {}
      if(this.printCheckAccountDialogResult.indexOf('summery') > -1) {
        summery = this.$refs.summaryView.getPrintInfo(this.queryData.sellerName)
      }
      if(this.printCheckAccountDialogResult.indexOf('detail') > -1) {
        detail = this.$refs.sheetsView.getPrintInfo(this.queryData.sellerName)
        //print({detail})
      }
      if(this.printCheckAccountDialogResult.indexOf('item') > -1) {
        item = this.$refs.itemsView.getPrintInfo(this.queryData.sellerName)
      }

      sheet = {
        summery: {
          ...summery,
          printStartAndEndTime : this.printStartAndEndTime
        },
        detail: {
          ...detail,
          printStartAndEndTime : this.printStartAndEndTime
        },
        item: {
          ...item,
          printStartAndEndTime : this.printStartAndEndTime
        },

      }
      var saleSheets=this.sheets.saleSheets.filter(sht=>sht.isChecked)
      Printing.printCheckAccountAll(sheet,saleSheets,this.printCheckAccountDialogResult, res => {
        if(res.result=="OK"){
         
          Toast.success('打印成功');
        }
        else{
          Toast.fail(res.msg)
        }
      })
     
    },
    printConfirm() {
      const toast = Toast.loading({
        message: '打印中...',
        forbidClick: true,
        duration:0,
        overlay:true
      })
      let that = this
      let tempTab = this.curTab
      localStorage.setItem('printCheckPrefs',JSON.stringify(this.printCheckAccountDialogResult));
      if(this.printCheckAccountDialogResult.indexOf('detail') > -1 && this.$refs?.sheetsView?.getPrintInfo == undefined) {
        that.curTab = 1
      }
      if(this.printCheckAccountDialogResult.indexOf('item') > -1 && this.$refs?.itemsView?.getPrintInfo == undefined) {
        setTimeout(() => {
          that.curTab = 2
        },300)
      }
      setTimeout(()=> {
        that.curTab = tempTab
        that.printfAcccount()
        toast.clear();
      },600)
    },
    printCancel() {
      this.printDialogShow = false
    },

    // 提交对账单
    onSubmitCheckAccount(){
      Dialog.confirm({
        title: '提示',
        message: '确认交账？',
        width:"320px"
      }).then(()=>{
        let sheetRows = []
        console.log('from:', this.sheets)
          //console.log('test:', this.sheets.saleSheets.filter(sheet=>sheet.isChecked==true)
         //.map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:sheet.sheet_type})))
        sheetRows = sheetRows.concat(this.sheets.saleSheets.filter(sheet=>sheet.isChecked==true)
          .map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:sheet.sheetType})))

        sheetRows = sheetRows.concat(this.sheets.borrowSheets.filter(sheet=>sheet.isChecked==true)
          .map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:sheet.sheetType})))

        sheetRows = sheetRows.concat(this.sheets.pregetSheets.filter(sheet=>sheet.isChecked==true)
          .map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:sheet.sheet_type})))
        sheetRows = sheetRows.concat(this.sheets.prepaySheets.filter(sheet=>sheet.isChecked==true)
            .map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:'YF'})))
        sheetRows = sheetRows.concat(this.sheets.getArrearSheets.filter(sheet=>sheet.isChecked==true)
          .map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:sheet.sheet_type})))
        sheetRows = sheetRows.concat(this.sheets.feeOutSheets.filter(sheet=>sheet.isChecked==true)
          .map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:'ZC'})))
        sheetRows = sheetRows.concat(this.sheets.incomeSheets.filter(sheet=>sheet.isChecked==true)
          .map(sheet=>({business_sheet_id:sheet.sheet_id, business_sheet_type:'SR'})))
        console.log('checked sheets:',sheetRows)
        if(sheetRows.length > 0){
          let params = {
            operKey: this.$store.state.operKey,
            getter_id: this.queryData.sellerID,

            sheetRows,
            start_time: this.start_time,
            end_time:this.end_time,
            happen_time:this.happen_time_show,

            sale_amount: this.$refs.summaryView.saleSum.real_get_amount, // 销售净额
            sale_total_amount: this.$refs.summaryView.saleSum.sale_amount,
            return_amount: this.$refs.summaryView.saleSum.return_amount,
            sale_prepay_amount: this.$refs.summaryView.saleSum.prepay_amount,
            sale_left_amount: this.$refs.summaryView.saleSum.left_amount,
            sale_disc_amount: this.$refs.summaryView.saleSum.disc_amount,

            get_preget: this.$refs.summaryView.pregetSheetSum.real_get_amount, // 收预收款
            preget_total_amount: this.$refs.summaryView.pregetSheetSum.preget_amount,
            preget_left_amount: this.$refs.summaryView.pregetSheetSum.left_amount,
            preget_disc_amount: this.$refs.summaryView.pregetSheetSum.disc_amount,

            get_prepay: this.$refs.summaryView.prepaySheetSum.real_get_amount, // 预付款
            prepay_total_amount: this.$refs.summaryView.prepaySheetSum.prepay_amount,
            prepay_left_amount: this.$refs.summaryView.prepaySheetSum.left_amount,
            prepay_disc_amount: this.$refs.summaryView.prepaySheetSum.disc_amount,

            get_arrears: this.$refs.summaryView.getArrearSheetSum.real_get_amount, // 收欠款
            arrears_disc_amount: this.$refs.summaryView.getArrearSheetSum.disc_amount,
            arrears_prepay_amount: this.$refs.summaryView.getArrearSheetSum.prepay_amount,
            arrears_total_amount: Number(this.$refs.summaryView.getArrearSheetSum.real_get_amount) + Number(this.$refs.summaryView.getArrearSheetSum.disc_amount) + Number(this.$refs.summaryView.getArrearSheetSum.prepay_amount),

            fee_out_total_amount: this.$refs.summaryView.feeOutSheetSum.fee_out_total_amount, //费用支出
            fee_out: this.$refs.summaryView.feeOutSheetSum.real_get_amount,
            fee_out_left_amount: this.$refs.summaryView.feeOutSheetSum.fee_out_left_amount,
            fee_out_disc_amount: this.$refs.summaryView.feeOutSheetSum.fee_out_disc_amount,

            income: this.$refs.summaryView.incomeSheetSum.real_get_amount,
            income_total_amount: this.$refs.summaryView.incomeSheetSum.income_total_amount,
            income_left_amount: this.$refs.summaryView.incomeSheetSum.income_left_amount,
            income_disc_amount: this.$refs.summaryView.incomeSheetSum.income_disc_amount
          }
          SubmitCheckAccount(params).then(res => {
            if (res.result === "OK") {
              Toast.success('对账成功')
              this.sheets.sheet_id = res.sheet_id
              this.sheets.happen_time = res.happen_time

              this.onloadData()
              this.dayCheckSheetsID = res.daySheetsID
            }
            else{
              Toast.fail(res.msg)
            }
          })
        }else{
          Toast.fail('未选择单据')
        }
      })

    },
    redCheckSheet(){
      if(this.sheets.sheet_id){
        Dialog.confirm({
          title:'提示',
          message: '确认红冲？',
          width:'320px'
        }).then(() => {
          let params = {
            operKey: this.$store.state.operKey,
            sheet_id: this.sheets.sheet_id
          }
          SheetCheckRed(params).then(res => {
            if (res.result === 'OK') {
              Toast.success('红冲成功')
              this.sheets.isRed = true
              this.seller_name += '(已红冲)'
            }else{
              Toast.fail('红冲失败，请检查网络连接')
            }
          })
        })
      }
    },
      popHappenTime() {

      this.happen_time = new Date()
      this.happenTimeShow = true


    },
    confirmHappenTime(value) {
      this.happenTimeShow = false
      this.happen_time = value
      this.happen_time_show =this.dateFormat("YYYY-mm-dd", this.happen_time)
    },
    changeHappenTime(value) {
      console.log(value)
     // this.happen_time = this.dateFormat("YYYY-mm-dd", value)
    },
    cancelHappenTime() {
      this.happenTimeShow = false
      this.happen_time = this.dateFormat("YYYY-mm-dd HH:MM:SS", new Date())
      this.happen_time_show =this.dateFormat("YYYY-mm-dd HH:MM:SS", new Date())
    },
    // dateFormat2(date) {
    //   var y = date.getFullYear();
    //   var m = date.getMonth() + 1;
    //   m = m < 10 ? '0' + m : m;
    //   var d = date.getDate();
    //   d = d < 10 ? ('0' + d) : d;
    //   return y + '-' + m + '-' + d;
    // },

    dateFormat(fmt, date) {
      let ret;
      var date = new Date(date);
      const opt = {
          "Y+": date.getFullYear().toString(),        // 年
          "m+": (date.getMonth() + 1).toString(),     // 月
          "d+": date.getDate().toString(),            // 日
          "H+": date.getHours().toString(),           // 时
          "M+": date.getMinutes().toString(),         // 分
          "S+": date.getSeconds().toString()          // 秒
          // 有其他格式化字符需求可以继续添加，必须转化成字符串
      };
      for (let k in opt) {
          ret = new RegExp("(" + k + ")").exec(fmt);
          if (ret) {
              fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
          };
      };
      return fmt;
     },
    showAllCheckSheets() {
      if(this.seller_name.includes(this.isAllSheet)) {
        return
      }
      let obj = {
        sheet_id:this.dayCheckSheetsID,
        sellerName: this.queryData.sellerName + '【' + this.isAllSheet+'】',
        happen_time: this.queryData.happen_time||this.sheets.happen_time,
        maker_id:this.queryData.maker_id || this.maker_id,
        dateTypeIds:this.queryData.dateTypeIds
      }
      this.$router.push({path:'/ShowAccount', query: obj})

      // LoadCheckSheet({
      //   sheetID:this.dayCheckSheetsID
      // }).then(res => {
      //   // console.log(res);
      //
      // }).catch(err=> {
      //   console.log(err);
      // })

    }
  }
};
</script>
<style lang="less" scoped>
@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_acent_jcen: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_acent_jcent: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_acent_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column
};
.report_no_box{
  width: 100%;
  height: calc(100% - 46px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon{
      font-size: 50px;
  }
  p{
      font-size: 14px;
  }
}
.showDate{
  width: 80px;
  height: 30px;
  background-color: rgba(255,255,255,.3);
  padding: 5px;
  border-radius: 15px;
}
/deep/.van-field__control{
  color: #1989fa;
  text-align: center;
}
/deep/.van-field__value{
  line-height: 20px;
}
.receive {
  padding: 5px;
  background: #ffffff;
  height: calc(100% - 88px);
  li {
    height: 40px;
    overflow: hidden;
    padding: 5px;
    border-bottom: 1px solid #f2f2f2;
    .receive_t{
      font-size: 14px;
      @flex_acent_jbw();
      div{
        flex: 1;
      }
      div:first-child{
        text-align: left;
      }
      div:nth-child(2){
        text-align: center;
      }
      div:last-child{
        text-align: right;
      }
    }
    .receive_b{
      font-size: 12px;
      color: #ccc;
      text-align: right;
      margin-top: 5px;
    }
  }
  li:last-child {
    border-bottom: none;
  }
}
.date_t{
  text-align: left;
  padding: 10px 30px 5px 30px;
  font-size: 16px;
  background-color: #ffffff;
}
.t_footer{
  width: 100px;
  height: 35px;
  overflow: hidden;
  border: none; /* 移除边框 */
  border-radius: 5px; /* 添加圆角 */
}
.footer{
  @flex_acent_jcen();
  background-color: #ffffff;
  height: 55px;
  border-radius: 5px; 
}
/deep/.van-tab{
  font-size: 16px;
}
/deep/.public_box2{
  height: calc(100% - 96px);
  .van-tabs{
    height:calc(100%);
  }

  .van-tabs__content{
    height:  calc(100% );
    margin-top: 1px;
  }
  .van-tab__pane{
    height: 100%;
  }
}
/deep/.check_date_type{
  background: transparent;
  color: #000!important;
  border:none;
  @flex_acent_jcent();
  height: 100%;
  font-size: 15px;
  i{
    font-size: 20px;
    margin-left: 5px;
  }
}
.van-tabs__wrap{
  border-bottom: 1px solid #f2f2f2;
}
.checkbox-group{
  display: flex;
  height: auto;
  min-height: 80px;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 12px;
}

.item-sub-options {
  width: 100%;
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.sub-option-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  font-weight: 500;
}

.item-sub-options .van-checkbox {
  margin-right: 15px;
  margin-bottom: 8px;
}
</style>
