<template>
  <div class="public_box4">
    <div class="receive_boxs">


<template v-if="itemSortOut.saleItemsTree.quantity">
      <div style="width:100%;height:15px;background-color:#f4f4f4"></div>
      <div class="van_divider">
        <div>
          <div>销售</div>
          <div  v-if="itemSortOut.saleItemsTree.sub_amount"> ￥{{toMoney(itemSortOut.saleItemsTree.sub_amount)}}</div>
        </div>
        <div v-if="itemSortOut.saleItemsTree.quantity">{{itemSortOut.saleItemsTree.quantity.nums[0]}}大{{itemSortOut.saleItemsTree.quantity.nums[1]}}中{{itemSortOut.saleItemsTree.quantity.nums[2]}}小</div>
        </div >
      <v-check-class-sum :sumRow="itemSortOut.saleItemsTree"  ></v-check-class-sum>

      </template>

      <template v-if="itemSortOut.returnItemsTree.quantity">
      <div style="width:100%;height:15px;background-color:#f4f4f4"></div>
      <div class="van_divider">
        <div>
          <div>退货</div>
          <div v-if="itemSortOut.returnItemsTree.sub_amount">￥{{toMoney(itemSortOut.returnItemsTree.sub_amount)}}</div>
        </div>
        <div  v-if="itemSortOut.returnItemsTree.quantity">{{itemSortOut.returnItemsTree.quantity.nums[0]}}大{{itemSortOut.returnItemsTree.quantity.nums[1]}}中{{itemSortOut.returnItemsTree.quantity.nums[2]}}小</div>
      </div>
      <v-check-class-sum :sumRow="itemSortOut.returnItemsTree"  ></v-check-class-sum>
      </template>

    <template v-if="itemSortOut.freeItemsTree.quantity">
        <div style="width:100%;height:15px;background-color:#f4f4f4"></div>
        <div class="van_divider">
          <div>
            <div>赠送</div>
            <div v-if="itemSortOut.freeItemsTree.sub_amount">￥{{toMoney(itemSortOut.freeItemsTree.sub_amount)}}</div>
          </div>
          <div  v-if="itemSortOut.freeItemsTree.quantity">{{itemSortOut.freeItemsTree.quantity.nums[0]}}大{{itemSortOut.freeItemsTree.quantity.nums[1]}}中{{itemSortOut.freeItemsTree.quantity.nums[2]}}小</div>
        </div>
        <v-check-class-sum :sumRow="itemSortOut.freeItemsTree"  ></v-check-class-sum>
  </template>

        

      <template v-if="itemSortOut.feeOutKSItemsTree.quantity">
        <div style="width:100%;height:15px;background-color:#f4f4f4"></div>
        <div class="van_divider">
          <div>
            <div>客损</div>
            <div  v-if="itemSortOut.feeOutKSItemsTree.sub_amount"> ￥{{toMoney(itemSortOut.feeOutKSItemsTree.sub_amount)}}</div>
          </div>
          <div v-if="itemSortOut.feeOutKSItemsTree.quantity">{{itemSortOut.feeOutKSItemsTree.quantity.nums[0]}}大{{itemSortOut.feeOutKSItemsTree.quantity.nums[1]}}中{{itemSortOut.feeOutKSItemsTree.quantity.nums[2]}}小</div>
        </div >
        <v-check-class-sum :sumRow="itemSortOut.feeOutKSItemsTree"  ></v-check-class-sum>

      </template>

      <template v-if="itemSortOut.borrowJHItemsTree.quantity">
        <div style="width:100%;height:15px;background-color:#f4f4f4"></div>
        <div class="van_divider">
          <div>
            <div>借货</div>
            <div></div>
          </div>
          <div v-if="itemSortOut.borrowJHItemsTree.quantity">{{itemSortOut.borrowJHItemsTree.quantity.nums[0]}}大{{itemSortOut.borrowJHItemsTree.quantity.nums[1]}}中{{itemSortOut.borrowJHItemsTree.quantity.nums[2]}}小</div>
        </div>
        <v-check-class-sum :sumRow="itemSortOut.borrowJHItemsTree"  ></v-check-class-sum>
     </template>
     <template v-if="itemSortOut.borrowHHItemsTree.quantity">
        <div style="width:100%;height:15px;background-color:#f4f4f4"></div>
        <div class="van_divider">
          <div>
            <div>还货</div>
            <div></div>
          </div>
          <div  v-if="itemSortOut.borrowHHItemsTree.quantity">{{itemSortOut.borrowHHItemsTree.quantity.nums[0]}}大{{itemSortOut.borrowHHItemsTree.quantity.nums[1]}}中{{itemSortOut.borrowHHItemsTree.quantity.nums[2]}}小</div>
        </div>
        <v-check-class-sum :sumRow="itemSortOut.borrowHHItemsTree"  ></v-check-class-sum>
     </template>
    </div>
    <!--
    <div class="acc_footer">
      <div>合计</div>
      <div>{{ sheetTotal }}</div>
    </div>
    -->
  </div>
</template>
<script>
import CheckClassSum from "./CheckClassSum"
import Printing from "../Printing/Printing"
import {Divider,Toast} from "vant"
export default {
  data() {
    return {
      sheetDataAttrs:[],
      sheetTotal:0,
      detailedList:[],
      sheeUnitDatas:[]
    }
  },
  mounted() {
    // console.log(this.sheets)
    // console.log('商品分拣',this.itemSortOut)
  },
  props:{
    sheets: Object
  },
  computed:{
    /*sumClasses(){

        var sumClasses=[]
        var sumRows={}
        var sumSubClasses=[]
        var that=this
        function add1RowToClassSum(classes,infoClasses,row){
              var mother_id=''

              var class_name=''
              infoClasses.forEach(function(cls) {
                 if(cls.class_id==row.class_id) {
                    mother_id=cls.mother_id
                    class_name=cls.class_name
                 }
              })

             if(!classes[row.class_id]) {
                  classes[row.class_id]={item_id:row.class_id, item_name:class_name,class_id:mother_id,quantity:""}
             }

             var classRow= classes[row.class_id]
             //if(!classRow.units[row.unit_no]) {
             //    classRow.units[row.unit_no]={unit_factor:Number(row.unit_factor),quantity:0}
             //}

             //classRow.units[row.unit_no].quantity+=row.quantity

              if(!classRow.quantity) classRow.quantity=''
             classRow.quantity= that.getClassUnitQuantity(classRow.quantity,row.quantity)
             if(!classRow.sub_amount) classRow.sub_amount=0
             classRow.sub_amount+=Number(row.sub_amount)
             if(!classRow.rows) classRow.rows=[]
             classRow.rows.push(row)
             if(mother_id=='0' || !mother_id) return
             add1RowToClassSum(classes,infoClasses,classRow)
        }
        var that=this
        this.sheets.saleSheets.forEach(function(sheet){
          sheet.sheetRows.forEach(function(row){
              row.rows=[]
              var sumRow=sumRows[row.item_id]
              if(!sumRow){
                sumRow=sumRows[row.item_id]={}
                sumRow.item_id=row.item_id
                sumRow.item_name=row.item_name
                sumRow.quantity=""
                sumRow.sub_amount=0
                sumRow.units={}
                sumRow.class_id=row.class_id
              }

              var sumRow=sumRows[row.item_id]
              sumRow.isItemOrClass=true
              sumRow.sub_amount+=Number(row.sub_amount)
              if(!sumRow.units[row.unit_no]){
                  sumRow.units[row.unit_no]={unit_factor:row.unit_factor,quantity:0}
              }
              sumRow.units[row.unit_no].quantity+=Number(row.quantity)
              sumRow.quantity = that.getOrderedUnitQuantity(sumRow.units,'')
          })
        })
        var classes=[]
        for(var key in sumRows){
            var sumRow=sumRows[key]
             add1RowToClassSum(classes,this.sheets.itemClasses,sumRow)
        }

        var rootClass={rows:[]}
        classes.forEach(function(cls){
          if(cls.class_id=='0'){
             rootClass=cls
          }
        })
        console.log('rootClass:',rootClass)
        return rootClass
    },*/
    itemSortOut(){
      var itemClasses = this.sheets.itemClasses
      var saleSheets = this.sheets.saleSheets
      var saleItemsTree = {type:'sale'}
      var returnItemsTree = {type:'return'}
      var borrowJHItemsTree = {type:'borrowJH'}
      var borrowHHItemsTree = {type:'borrowHH'}
      
      var freeItemsTree = {type:'free'}
      var feeOutKSItemsTree = { type: 'feeOutKS' }
      var itemsTree

      var itemSheets=[]
      if(this.sheets.saleSheets){
        this.sheets.saleSheets.forEach(function(sheet){
          itemSheets.push(sheet)
        })
      }
      if(this.sheets.borrowSheets ){
        this.sheets.borrowSheets.forEach(function(sheet){
          itemSheets.push(sheet)
        })
      }

     // if(!saleSheets && ! this.sheets.borrowSheets) return {saleItemsTree,borrowJHItemsTree,borrowHHItemsTree,returnItemsTree,freeItemsTree, feeOutKSItemsTree}
      // 遍历saleSheet（实际包括销售单与退货单）

      itemSheets.forEach(sheet => {
      var that = this
      if( !that.$store.state.checkedSheetsCount || sheet.isChecked){
        // 判断是否是销售单
       // var isSale = sheet.sheetType == 'X' ? true : false
        console.log('sheet', sheet.sheet_no)
        sheet.sheetRows.forEach(item => {
          // 将商品自身id也放进类别路径，以方便操作
          if(typeof(item.other_class)=='string'){
            item.other_class = getClasses(item.other_class)
            item.other_class.splice(2, item.other_class.length - 2)
            item.other_class.push(item.item_id)
          }
          // 根据标记判断应该将商品插入哪一棵树
          var sub_amount = Number(item.sub_amount)
          if(sheet.sheetType == 'X'){
            if (sub_amount > 0) {
              if(item.trade_type === 'KS') {
                itemsTree = feeOutKSItemsTree
              } else {
                itemsTree = saleItemsTree
              }
            }
            else if(sub_amount == 0)
              itemsTree = freeItemsTree
            else
              itemsTree = returnItemsTree

            this.sheetTotal += sub_amount
          }
          else if(sheet.sheetType == 'T'){
            itemsTree = returnItemsTree
            this.sheetTotal -= item.sub_amount
            sub_amount = - sub_amount
          }
          else if(sheet.sheetType == 'JH'){
            itemsTree = borrowJHItemsTree 
          }
          else if(sheet.sheetType == 'HH'){
            itemsTree = borrowHHItemsTree
          }

          // 根节点判断
          if(!itemsTree.class_id){
            itemsTree.class_id = item.other_class[0]
            itemsTree.item_name = getClassInfo(item.other_class[0]).item_name
            itemsTree.quantity = newQuantity(item)
            itemsTree.sub_amount = sub_amount
            itemsTree.rows = []
          }else{
            if(itemsTree.class_id != item.other_class[0]){
              item.other_class[0] = itemsTree.class_id
              itemsTree.sub_amount += sub_amount
              return
            }else{
              itemsTree.sub_amount += sub_amount
              //itemsTree.quantity = addQuantity(itemsTree.quantity, item.quantity, item.unit_no, item.unit_type,item)
            }
          }
          // 根据商品的类别路径遍历树，边建树边插入
          if(item?.other_class) {


          for(var i = 1; i < item.other_class.length; ++i){
            var class_id = item.other_class[i]
            var flag = false
            let bForItem = i==item.other_class.length-1 ? true : false
            let tempTree = {}
            // 同时根据id排序
            for(var j = 0; j < itemsTree.rows.length; ++j){
              tempTree = itemsTree
              if(itemsTree.rows[j].class_id == class_id){
                itemsTree = itemsTree.rows[j]

               if (i === 1 ) {//此处没有考虑同商品多种备注分类汇总，
                 itemsTree.sub_amount += sub_amount
               }

                if(itemsTree.rows.length == 0) {
                  let tempArrpushFlag = true
                  for(let tempi = 0 ; tempi < tempTree.rows.length; tempi ++) {
                    if(((!tempTree.rows[tempi].remark && !item.remark) || (item.remark == tempTree.rows[tempi].remark)) && item.item_name == tempTree.rows[tempi].item_name ) {

                      itemsTree = tempTree.rows[tempi]
                      itemsTree.sub_amount += sub_amount

                      itemsTree.quantity = addQuantity(itemsTree.quantity,item)
                      tempArrpushFlag = false
                      break
                    }
                  }
                  if(tempArrpushFlag) {
                    tempTree.rows.push({
                      ...(item),
                      class_id: class_id,
                      quantity: newQuantity(item,bForItem),
                      sub_amount: sub_amount,
                      rows: [],
                    })
                  }

                }

                flag = true
                break
              }
              if(itemsTree.rows[j].class_id < class_id){
                itemsTree.rows.splice(j, 0, {
                  ...(bForItem ? item : getClassInfo(class_id)),
                  class_id: class_id,
                  quantity: newQuantity(item,bForItem),
                  sub_amount: sub_amount,
                  rows: [],
                })
                itemsTree = itemsTree.rows[j]
                flag = true
                break
              }
            }
           if(!flag){
             console.log(itemsTree.rows)
              var len = itemsTree.rows.push({
                ...(bForItem ? item : getClassInfo(class_id)),
                class_id: class_id,
                quantity: newQuantity(item, bForItem),
                sub_amount: sub_amount,
                rows: [],
              })
             tempTree = itemsTree
             itemsTree = itemsTree.rows[len - 1]
            }
           // if(remarkFlag) {
           //   itemsTree.rows.rows.push(item)
           // }
          }
           // console.log('当前树:', JSON.parse(JSON.stringify(itemsTree)))
           // console.log('销售树:', JSON.parse(JSON.stringify(saleItemsTree)))
          // console.log('退货树:', JSON.parse(JSON.stringify(returnItemsTree)))
          // console.log('赠品树:', JSON.parse(JSON.stringify(freeItemsTree)))
          }
        })
      }
      })
      getClassQty(saleItemsTree)
      getClassQty(returnItemsTree)
      getClassQty(freeItemsTree)
      getClassQty(feeOutKSItemsTree)

      getClassQty(borrowJHItemsTree)
      getClassQty(borrowHHItemsTree)
      
      
      compareTreeArr(saleItemsTree,'remark')
      compareTreeArr(returnItemsTree,'remark') 
      compareTreeArr(freeItemsTree,'remark')
      compareTreeArr(feeOutKSItemsTree,'remark')

      compareTreeArr(borrowJHItemsTree,'remark')  
      compareTreeArr(borrowHHItemsTree,'remark')

      // compareTreeArr(saleItemsTree,'item_id')
      // compareTreeArr(returnItemsTree,'item_id')
      // compareTreeArr(freeItemsTree,'item_id')

      // 对数据进行排序

      // 数据库可能保存商品的完整或不完整类别路径，所以需要在解析类别
      // 路径字符串的基础上进行校验和补全
      function getClasses(classesStr){
          var needVerify = false
          if (typeof classesStr != 'string' || !classesStr) {
              return ['0','0']
          }
        // 分隔并去除空串
        var classes = classesStr.split('/').filter(i=>i).map(i => i.replace(/\s+/, ''))

        if(needVerify){
          // 校验（待完成）
        }
        return classes
      }
      function getClassInfo(class_id){
        if (class_id == 0) {
            return {
                item_name: '其他',
                class_id: 0,
            }
        }
        var index = itemClasses.findIndex(c => c.class_id == class_id)
        if(index == undefined || index == -1)
          return {}
        else
          return {
            item_name: itemClasses[index].class_name,
            class_id: class_id,
          }
      }
      function newQuantity(item,bForItem){
        //console.log("newQuantity", item)
        var quantity = {
          nums: [0, 0, 0],
          s_quantity: 0,
          unit_nos: ['', '', ''],
          unit_types: ['大', '中', '小'],
          sumStr: function (){
            var str = ''
            for(var i = 0; i < this.nums.length; ++i){
              if(this.nums[i] != 0)
                str +=   toMoney(this.nums[i],4) + this.unit_types[i]
            }
            return str
          },
          detailStr: function (){
            var str = ''
            for(var i = 0; i < this.nums.length; ++i){
              if(this.nums[i] != 0)
                str += toMoney(this.nums[i],4) + this.unit_nos[i]
            }
            return str
          }
        }
        if(!bForItem) return quantity
        return addQuantity(quantity, item)
      }
      function addQuantity(qty,item){
        //console.log("addQuantity", item)
        qty.s_quantity +=  Math.abs(Number(item.quantity * item.unit_factor))
        let leftQty = toMoney(Number(qty.s_quantity),4)
        if(item.b_unit_factor) {
           qty.nums[0]  = parseInt(leftQty / Number(item.b_unit_factor))
           leftQty = leftQty % Number(item.b_unit_factor)
        }
        if(item.m_unit_factor) {
          qty.nums[1]  = parseInt(leftQty / Number(item.m_unit_factor))
          leftQty = leftQty % Number(item.m_unit_factor)
        }
        qty.nums[2]  = leftQty
        qty.unit_nos[0] = item.b_unit_no
        qty.unit_nos[1] = item.m_unit_no
        qty.unit_nos[2] = item.s_unit_no
        // var index
        // if(item.unit_no == item.b_unit_no){   //b_unit_factor  s_unit_factor
        //   index = 0
        // }else if(item.unit_no == item.m_unit_no){
        //   index = 1
        // }else{
        //   index = 2
        // }
        //qty.nums[index] += Math.abs(Number(num))
        // if(!qty.unit_nos[index])
        //   qty.unit_nos[index] = item.unit_no
        return qty
      }
      function getClassQty(cls) {
        var num = [0,0,0]
        if(cls.rows  && cls.rows.length > 0) {
          cls.rows.forEach(row => {
            var tempNum = getClassQty(row)
            num[0] += tempNum[0]
            num[1] += tempNum[1]
            num[2] += tempNum[2]
          })
          cls.quantity.nums = num
          return num
        } else {
          return cls.quantity ? cls.quantity.nums : null
        }
      }

      /**
       * 对象数组比较
       * @param prop
       * @returns {(function(*, *): (number))|*}
       */
      function compareArrObj(property) {
        return function (obj1, obj2) {
          let val1 = obj1[property]
          let val2 = obj2[property]
          if(val1 == null || val1 == undefined) {
            val1 = ""
          }
          if(val2 == null || val2 == undefined) {
            val2 = ""
          }
          return val1.localeCompare(val2)
          // var val1 = obj1[prop];
          // var val2 = obj2[prop];
          // if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
          //   val1 = Number(val1);
          //   val2 = Number(val2);
          // }
          // if (val1 < val2) {
          //   return -1;
          // } else if (val1 > val2) {
          //   return 1;
          // } else {
          //   return 0;
          // }
        }
      }
      function compareTreeArr(tree,prop) {
        if(tree?.rows !== undefined) {
          tree.rows.forEach(rowsTemp => {
            rowsTemp.rows.sort(compareArrObj(prop))

          })
        }
      }
      return{
        saleItemsTree,
        borrowJHItemsTree,
        borrowHHItemsTree,
        returnItemsTree,
        freeItemsTree,
        feeOutKSItemsTree
      }
    }
  },

  // 监听明细单选择业务员是否发生变化
  watch:{
    /*'bindOnloadItems':{
      immediate: true,
      handler(obj){
        this.onloadData(obj);
      }
    }*/
  },
  components: {
    "van-divider": Divider,
    "v-check-class-sum":CheckClassSum
  },
  methods: {
    toMoney(amount) {
      if (amount) amount = parseFloat(amount)
        if(typeof(amount)=='undefined'){
            return amount
        }else{
             if (amount.toFixed)
             amount = amount.toFixed(4)
        }
      amount = amount.toString()
      if (amount.indexOf('.0000') > 0) {
          amount = amount.replace('.0000', '')
      }
      var d = amount.indexOf('.')
      if (d > 0) {
          for (var i = 4; i >= 1; i--) {
              var n = amount.lastIndexOf('0'.myPadLeft(i, '0'))
              if (n > d && n == amount.length - i ) {
                  amount = amount.substr(0, amount.length - i)
              }
          }
          d = amount.indexOf('.')
          if (d == amount.length - 1) amount = amount.substr(0, amount.length - 1)
      }
      return amount
    },
    // 初始化数据
    getOrderedUnitQuantity(units,orderedUnits){
      units=JSON.parse(JSON.stringify(units))
      var max={unit_factor:0}
      var maxUnitNo='',maxUnitQty=0
      for(var key in units){
        var unit=units[key]
        if(unit.unit_factor>max.unit_factor){
          max=unit
          maxUnitNo=key
          maxUnitQty=unit.quantity
        }
      }
      if(maxUnitNo){
          delete units[maxUnitNo]
          orderedUnits+=maxUnitQty + maxUnitNo
      }
      var ct=0
      for(var key in units){
        ct++
      }
      if(ct==0){
        return orderedUnits
      }
      else return this.getOrderedUnitQuantity(units,orderedUnits)
    },
    getClassUnitQuantity(curQtyUnit,addQtyUnit){
      var n1=-1,n2=-1,next=0
      var result=''
      for(var i=0;i<addQtyUnit.length;i++){
        var s=addQtyUnit.substr(i,1)
        if(n1==-1 && ('**********'.indexOf(s)>=0 || i==addQtyUnit.length-1)){
          n1=i;
        }
        else if(n1!=-1 && '**********'.indexOf(s)==-1){
          n2=i
          var qty=addQtyUnit.substring(n1,n2)
          qty=Number(qty)
          var unit=addQtyUnit.substr(n2,1)
          result+=this._getClassUnitQuantity(curQtyUnit,qty,unit)
          n1=-1
        }
      }
      return result
    },
    _getClassUnitQuantity(curQtyUnit,add_qty,add_unit){
      var n=curQtyUnit.indexOf(add_unit)
      if(n==-1)
        return curQtyUnit+add_qty+add_unit
        var n1=0
      for(var i=n;i>=0;i--){
        var s=curQtyUnit.substr(i,1)
        if('**********'.indexOf(s)==-1){
          n1=i+1
          break
        }
      }
      var qty=curQtyUnit.substring(n1,n)
      qty=Number(qty)
      var qty1=add_qty+qty
      curQtyUnit=curQtyUnit.replace(qty+add_unit,qty1+add_unit)
      return curQtyUnit
    },
    onloadData(oBject) {
      let obj = oBject.datasAttr
      if (obj) {
        // 获取showAccount中传来的值，把他们从循环中取出来并重洗组成一个数组
        if (obj.sheets&&obj.sheets.length>0) {
          let sheetItem = []
          let sheetItemDatas = []
          let sheetUnitNo = []
          let Ttotal = 0
          if (oBject.isType === 0){
            obj.sheets.map(item=>{
              item.sheetRows.map(item_son=>{
                sheetItem.push(item_son)
              })
            })
          } else {
            if (oBject.isActive&&oBject.isActive.length>0) {
              obj.sheets.map(item=>{
                oBject.isActive.map(itemActive=>{
                  if (item.sheet_id === itemActive){
                    if (item.sheetRows&&item.sheetRows.length>0){
                     // console.log(item.sheetRows)
                      item.sheetRows.map(itemRowActive=>{
                        sheetItem.push(itemRowActive)
                      })
                    } else {
                      sheetItem = []
                    }
                  }
                })
              })
            }
          }
          sheetItem.map(item=>{
            Ttotal+=Number(item.sub_amount)
            if (sheetItemDatas.length<=0){
              let datasAttrs = []
              datasAttrs.push(item)
              let objs = {
                class_name:item.class_name,
                datas:datasAttrs
              }
              sheetItemDatas.push(objs)
            } else {
              sheetItemDatas.map(item_son=>{
                if (item_son.class_name === item.class_name){
                  item_son.datas.push(item)
                } else {
                  let datasAttrs = []
                  datasAttrs.push(item)
                  let objs = {
                    class_name:item.class_name,
                    datas:datasAttrs
                  }
                  sheetItemDatas.push(objs)
                }
              })
            }
            if(sheetUnitNo.length<=0){
              let objs = {
                unit_no:item.unit_no,
                quantity:item.quantity
              }
              sheetUnitNo.push(objs)
            } else {
              let isUnit = {
                isPyay_ :false,
                datas:{}
              }
              sheetUnitNo.map(unit_son=>{
                if (unit_son.unit_no === item.unit_no){
                  let unitSum = Number(unit_son.quantity)
                  unit_son.quantity = unitSum+Number(item.quantity)
                  let objes = {
                    isPyay_ :false,
                    datas:{}
                  }
                  isUnit = objes
                } else {
                  let objs = {
                    unit_no:item.unit_no,
                    quantity:item.quantity
                  }
                  let objes = {
                    isPyay_:true,
                    datas:objs
                  }
                  isUnit = objes
                }
              })
              if (isUnit.isPyay_){
                sheetUnitNo.push(isUnit.datas)
              }
            }
            return item
          })
          this.sheetDataAttrs = sheetItemDatas
          this.sheeUnitDatas = sheetUnitNo
          this.sheetTotal = Ttotal
        }
      }
    },
    onClickLeft() {
      myGoBack(this.$router)
    },
    dateFormat(fmt, date) {
      let ret
      const opt = {
          "Y+": date.getFullYear().toString(),        // 年
          "m+": (date.getMonth() + 1).toString(),     // 月
          "d+": date.getDate().toString(),            // 日
          "H+": date.getHours().toString(),           // 时
          "M+": date.getMinutes().toString(),         // 分
          "S+": date.getSeconds().toString()          // 秒
          // 有其他格式化字符需求可以继续添加，必须转化成字符串
      }
      for (let k in opt) {
        ret = new RegExp("(" + k + ")").exec(fmt)
        if (ret) {
          fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
        }
      }
      return fmt
    },
    getPrintInfo(sellerName){
      return {
        ...this.itemSortOut,
        sellerName: sellerName,
        happen_time: this.sheets.happen_time || this.dateFormat("YYYY-mm-dd HH:MM", new Date()),
      }
    },
    fix(num, n=2){
      var pn = Math.pow(10, n);
      if(num) {
        return Number(Math.round(num * pn) / pn);
      } else {
        return 0;
      }
    },
    print(sellerName, printStartAndEndTime, itemTypes = ['sale', 'return', 'free']){
      var sheet = this.getPrintInfo(sellerName)
      sheet.printStartAndEndTime = printStartAndEndTime
      Printing.printCheckAccountItem(sheet, itemTypes, res => {
        if(res.result=="OK"){
          Toast.success('打印成功')
        }
        else{
          Toast.fail(res.msg)
        }
      })
    }
  }
};
</script>
<style lang="less" scoped>
@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};

@flex_acent_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column
};
.public_box4{
  height: 96.5%;
  overflow: hidden;
}
.receive_boxs{
  height:  100%;
  overflow-x: hidden;
  overflow-y: auto;
  .van_divider{
    // #1989fa

    color: #000000;
    border-bottom: 1px #eee solid;
    background-color:  #fff;
    padding: 5px 20px;
    display: flex;
    justify-content: space-between;
    div{
      font-size: 16px;
      font-weight: normal;
    }
    :first-child{
      flex: 2;
      text-align: right;
      display: flex;
      justify-content: space-between;
      :first-child{
         font-weight: bold;
      }
    }
    :last-child{
      flex: 1;
    }
  }
    .van_divider_num{
    // #1989fa
    color: #000000;
    border-color: #aaa;
    background-color:  #ffffff;
    margin:0;
    padding: 0px 10px 0px 10px;
    h5{
      font-size: 16px;
      font-weight: normal;
    }
  }

}

.acc_footer{
  height: 30px;
  overflow: hidden;
  font-size: 15px;
  padding: 0 10px;
  @flex_acent_jbw();
  background: #ffffff;
  border-top: 1px solid #f2f2f2;
  box-shadow: 1px 1px 1px #cccccc;
  div{
    flex: 1;
    text-align: center;
  }
  div:first-child{
    text-align: left;
  }
  div:last-child{
    text-align: right;
  }
}
.accountItems{
  height: auto;
  overflow: hidden;
  .title4{
    height: 40px;
    font-size: 16px;
    background: #fff;
    border: 1px solid #f2f2f2;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 5px;
  }
  .receive_boxs_ul{
    height: auto;
    overflow: hidden;
    background-color: #ffffff;
    padding: 0 8px;
    li{
      height: auto;
      overflow: hidden;
      @flex_acent_jbw();
      padding: 10px 5px;
      border-bottom: 1px solid #f2f2f2;
      div{
        flex: 1;
        text-align: center;
        font-size: 15px;
      }
      div:first-child{
        text-align: left;
      }
      div:last-child{
        text-align: right;
      }
    }
    li:last-child{
      border-bottom: none;
    }
  }
}

</style>
