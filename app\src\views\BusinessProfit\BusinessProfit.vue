<template>
  <div class="pages">
    <van-nav-bar title="经营利润表" 
      left-arrow 
      @click-left=" myGoBack($router)"
      safe-area-inset-top
    >
    </van-nav-bar>
    <div class="public_box">
      <div class="public_query_title">
        <van-cell-group class="cellgroup">
          <yj-select-calendar-cache
          :start-time-fld-name.sync="queryCondiValues.startDate"
          :end-time-fld-name.sync="queryCondiValues.endDate"
          :cache-key="'BusinessProfitKey'"
          @handleConfirm=""
          :options-btn="[
                  {key: '1-day', name: '今天'},
                  {key: 'yesterday', name: '昨天'},
                  {key: '2-day', name: '近2天'},
                  {key: 'currentMonth', name: '本月'}
                  ]"
          />
        </van-cell-group>
        <van-cell-group class="public_query_titleSrc_item">
          <select-one class="selectOne" style="width:85%;" inputAlign="left" placeholder="业务员" label="业务员" :hasClearBtn="false" :target="'seller'" :formObj="queryCondiValues" :formFld="'sellerId'" :formNameFld="'sellerName'">
            <template #left-icon>
              <svg class="icon black" style="margin-left:0px;" width="20px" height="20px" stroke-width="1.3">
                <use :xlink:href="'#icon-seller'"></use>
              </svg>
            </template>
          </select-one>
        </van-cell-group>
        <div class="public_query_titleSrc_checkbox">
          <van-checkbox v-model="queryCondiValues.isChecked" checked-color="#ff7766" icon-size="20px" shape="square">核算盘点单</van-checkbox>
          <!-- <van-button color="#ff99aa" @click="queryData"  class="t_footer" >查询</van-button> -->
        </div>
      </div>
      <ConcaveDottedCenter />
      <div style="padding:0 10px;display: flex;flex-direction: column;height: calc(100% - 230px);box-sizing: border-box;">
        <div class="title" style="margin-top: 6px;">
          <van-row>
            <van-col style="text-align: left;display:flex;justify-content:space-between;" span="12">
              <div>项目</div>
            </van-col>
            <van-col style="text-align: right;" span="12">金额</van-col>
          </van-row>
        </div>
        <div class="receive_boxs">
          <ul class="receive">
            <li>
              <div class="receive_item">
                <div class="level_1">销售收入</div>
                <div class="link" @click="toCustomerRanking">￥{{ parseFloat(incomeAndcost[0].income)||0}}</div>
              </div>
            </li>
            <li>
              <div class="receive_item">
                <div class="level_1">优惠合计</div>
                <div @click="toViewSheetAll('sheetSale')">￥{{(toMoney(Number(skDiscAmount) - Number(fkDiscAmount) + Number(incomeAndcost[0].dsc) + Number(buydisk[0].amount) ))||'0'}}</div>
              </div>
            </li>

            <li v-if="incomeAndcost[0].dsc">
              <div class="receive_item">
                <div class="level_2">销售优惠</div>
                <div class="link" @click="toViewSheetAll('sheetSale')">￥{{toMoney(incomeAndcost[0].dsc)||0}}</div>
              </div>
            </li>
          
            <li v-if="buydisk[0].amount">
              <div class="receive_item">
                <div class="level_2">采购优惠</div>
                <div class="link" @click="toViewSheetAll('sheetBuy')">￥{{toMoney(buydisk[0].amount)||'0'}}</div>
              </div>
            </li>

            <li v-if="skDiscAmount!=0">
              <div class="receive_item">
                <div class="level_2">收款优惠</div>
                <div class="link" @click="toViewSheetAll('sheetGetArrears')">￥{{toMoney(skDiscAmount)||'0'}}</div>
              </div>
            </li>
            <li v-if="fkDiscAmount!=0">
              <div class="receive_item">
                <div class="level_2">付款优惠</div>
                <div class="link" @click="toViewSheetAll('sheetPayArrears')" ref="closingBalance_1">{{toMoney(-fkDiscAmount)||'0'}}</div>
              </div>
            </li>
            <li v-if="fkDiscAmount!=0">
              <div class="receive_item">
                <div class="level_2">付款单优惠</div>
                <div class="link" @click="toViewSheetAll('sheetPayArrears')" ref="closingBalance_1">{{toMoney(-fkDiscAmount)||'0'}}</div>
              </div>
            </li>
            <li>
              <div class="receive_item">
                <div class="level_1">成本合计</div>
                <div @click="toCustomerRanking">￥{{(toMoney(toMoney(incomeAndcost[0].cost) +toMoney(incomeAndcost[0].free_cost)))||0}}</div>
              </div>
            </li>
            <li v-if="incomeAndcost[0].cost">
              <div class="receive_item">
                <div class="level_2">销售成本</div>
                <div class="link" @click="toCustomerRanking">￥{{toMoney(incomeAndcost[0].cost)||0}}</div>
              </div>
            </li>
            <li v-if="incomeAndcost[0].free_cost">
              <div class="receive_item">
                <div class="level_2">赠品成本(兑奖)</div>
                <div class="link" @click="toCustomerRanking">￥{{toMoney(incomeAndcost[0].free_cost_change)||'0'}}</div>
              </div>
              <div class="receive_item">
                <div class="level_2">赠品成本(陈列)</div>
                <div class="link" @click="toCustomerRanking">￥{{toMoney(incomeAndcost[0].free_cost_display)||'0'}}</div>
              </div>
              <div class="receive_item">
                <div class="level_2">赠品成本(赠品)</div>
                <div class="link" @click="toCustomerRanking">￥{{toMoney(incomeAndcost[0].free_cost_gift)||'0'}}</div>
              </div>
              <div class="receive_item">
                <div class="level_2">赠品成本(其他)</div>
                <div class="link" @click="toCustomerRanking">￥{{toMoney(incomeAndcost[0].free_cost_other)||'0'}}</div>
              </div>
            </li>
            <li>
              <div class="receive_item">
                <div class="level_1">销售利润</div>
                <!-- <div class="link" @click="toCustomerRanking">￥{{toMoney(parseFloat(incomeAndcost[0].income -incomeAndcost[0].free_cost-incomeAndcost[0].dsc-skDiscAmount[0].amount- incomeAndcost[0].cost ))||'0'}}</div> -->
                <div class="link" @click="toCustomerRanking">￥{{toMoney(parseFloat(incomeAndcost[0].income -incomeAndcost[0].free_cost- incomeAndcost[0].cost ))||'0'}}</div> 
              </div>
            </li>
            <li>
              <div class="receive_item">
                <div class="level_1">其他收入合计</div>
                <div class="link">￥{{toMoney(otherincome_amout)||'0'}}</div>
              </div>
            </li>
            <li v-for="(item,index) in otherincome_detail" :key="item.sub_name">
              <div class="receive_item">
                <div class="level_3">{{ item.sub_name }}</div>
                <div class="link">￥{{toMoney(item.detail)}}</div>
              </div>
            </li>
            <li>
              <div class="receive_item">
                <div class="level_1">费用合计</div>
                <div class="link">￥{{toMoney(fee_amout)||'0'}}</div>
              </div>
            </li>
            <li v-for="(item,index) in fee_detail" :key="item.sub_name">
              <div class="receive_item">
                <div class="level_3">{{ item.sub_name }}</div>
                <div class="link">￥{{toMoney(item.detail)}}</div>
              </div>
            </li>
            <li v-if="queryCondiValues.isChecked">
              <div class="receive_item">
                <div class="level_1">仓库盈亏合计</div>
                <div>￥{{toMoney(parseFloat(inventory[0].cost_amount -bs[0].cost_amount))||'0'}}</div>
              </div>
            </li>
            <li v-if="!queryCondiValues.isChecked">
                <div class="receive_item">
                <div class="level_1">仓库盈亏合计</div>
                <div>￥{{toMoney(bs[0].cost_amount) || '0' }}</div>
                </div>
            </li>
            <li v-if="inventory[0].cost_amount&&queryCondiValues.isChecked">
              <div class="receive_item">
                <div class="level_2">盘点盈亏</div>
                <div class="link" @click="toViewSheetAll('sheetInvent')">￥{{toMoney(inventory[0].cost_amount)||'0'}}</div>
              </div>
            </li>
            <li v-if="bs[0].cost_amount&& queryCondiValues">
              <div class="receive_item">
                <div class="level_2">报损</div>
                <div class="link" @click="toViewSheetAll('sheetInventReduce')">￥{{toMoney(bs[0].cost_amount)||'0'}}</div>
              </div>
            </li>
            <li v-if="queryCondiValues.isChecked">
              <div class="receive_item">
                <div class="level_1">净利润</div>
                <div>￥{{toMoney(parseFloat(incomeAndcost[0].income - skDiscAmount + fkDiscAmount -incomeAndcost[0].dsc- incomeAndcost[0].cost-incomeAndcost[0].free_cost +otherincome_amout-fee_amout  - bs[0].cost_amount - inventory[0].cost_amount*(-1)))||'0'}}</div>
              </div>
            </li>
     
            <li v-if="!queryCondiValues.isChecked">
              <div class="receive_item">
                <div class="level_1">净利润</div>
                <div>￥{{toMoney(parseFloat(incomeAndcost[0].income - skDiscAmount + fkDiscAmount -incomeAndcost[0].dsc- incomeAndcost[0].cost-incomeAndcost[0].free_cost +otherincome_amout-fee_amout - bs[0].cost_amount))||'0'}}</div>
              </div>
            </li>
           
          </ul>
        </div>
      </div>
      <!-- 底部 -->
    </div>
    <div class="footer" style="display: none;">
      <van-button style="background-color:#fafafa;"  class="t_footer" >导出</van-button>
      <van-button style="background-color:#fafafa;margin-left:20px"  class="t_footer" @click="doPrint()">打印</van-button>
    </div>
  </div>
</template>

<script>
import { NavBar, CellGroup, Field, Icon, Checkbox, Row, Col, Button, Toast, showConfirmDialog } from "vant"
import YjSelectCalendarCache from "../components/yjSelect/yjCalendar/YjSelectCalendarCache.vue";
import SelectOne from "../components/SelectOne";
import ConcaveDottedCenter from "../components/ConcaveDottedCenter";
import { GetBusinessProfit } from "../../api/api";

export default {
  name:"BusinessProfit",
  data () {
    return {
      showDate: false,
      queryCondiValues: {
        startDate: new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)) + '-' + new Date().getDate(),
        endDate:new Date().getFullYear() + '-' + ((new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : (new Date().getMonth() + 1)) + '-' + new Date().getDate(),
        sellerId:'',
        sellerName:'',
        isChecked:true
      },
      // queryCondiLabels: {
      //   dateTimeInfo:''
      // },
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
      // isChecked:true,
      otherincome_detail:[],
      fee_detail:[],
      incomeAndcost: [{ income: 0, cost: 0 ,free_cost:0}],
      otherincome_amout: 0,
      inventory:[{ cost_amount: 0 }],
      bs:[{ cost_amount: 0 }],
      buydisk: [{amount:0}], 
      skDiscAmount: { amount: 0 },
      fkDiscAmount: { amount: 0 },
      fee_amout: 0,
      printFlag:false
    }
  },
  components: {
    "van-nav-bar": NavBar,
    "van-cell-group": CellGroup,
    "van-field": Field,
    "van-icon":Icon,
    "van-checkbox":Checkbox,
    "van-row":Row,
    "van-col":Col,
    "van-button":Button,
    "select-one": SelectOne,
    ConcaveDottedCenter,
    YjSelectCalendarCache
  },
  watch: {
    queryCondiValues: {
      handler:function(nv,ov) {
        this.queryData()
      },
      deep: true
    }
  },
  mounted() {
    // this.queryCondiLabels.dateTimeInfo=this.queryCondiValues.startDate+"至"+this.queryCondiValues.endDate
    // this.queryData()
  },
  methods: {
    queryData() {
      let params = {
        startTime: this.queryCondiValues.startDate,
        endTime: this.queryCondiValues.endDate,
        operId:this.queryCondiValues.sellerId
      }
      this.getBusinessData(params)
    },
    getBusinessData(param) {
      GetBusinessProfit(param).then((res)=> {
        let that= this
        if (res.result === 'OK') {
          that.otherincome_detail=res.otherincome_detail
          that.fee_detail=res.fee_detail.filter(item => {
              // 排除 sub_code = 0 的科目（全部）
              if (item.sub_code === 0 || item.sub_code === '0') {
                  return false;
              }
              // 排除 sub_code < 10 的科目
              if (item.sub_code && parseInt(item.sub_code) < 10) {
                  return false;
              }
              // 排除名称中包含"损益类"的科目
              if (item.sub_name && item.sub_name.includes('损益类')) {
                  return false;
              }
              // 排除名称为"全部"的科目
              if (item.sub_name && item.sub_name === '全部') {
                  return false;
              }
              return true;
          });
          that.incomeAndcost=res.incomeAndcost
          that.inventory=res.inventory
          that.bs=res.bs
         // that.skDiscAmount=res.skdiscamount
          that.buydisk=res.buydisk
          that.skDiscAmount = parseFloat(res.skdiscamount.find(r=>r.sheet_type=='SK')?res.skdiscamount.find(r=>r.sheet_type=='SK').amount: 0);
          that.fkDiscAmount =  parseFloat(res.skdiscamount.find(r=>r.sheet_type=='FK')?res.skdiscamount.find(r=>r.sheet_type=='FK').amount: 0);
                           
          let sum1= 0
          that.otherincome_detail.forEach(item => {
            sum1 += Number(item.detail)
          })
          that.otherincome_amout= sum1
          let sum2= 0
          that.fee_detail.forEach(item =>{
            sum2 += Number(item.detail)
          })
          that.fee_amout = sum2
        }else {
          Toast.fail(res.msg)
        }
      })
    },
    // onConfirm(date) {
    //   const [start, end] = date;
    //   this.showDate = false;
    //   this.queryCondiValues.startDate = `${this.formatDate(start)}`
    //   this.queryCondiValues.endDate = `${this.formatDate(end)}`
    //   this.queryCondiLabels.dateTimeInfo =this.queryCondiValues.startDate + " 至 " + this.queryCondiValues.endDate;
    // },
    toCustomerRanking() {
      this.$router.push({ path:'/CustomerRanking',query:{
        types:"productSalesSum",
        names:"热销商品排行",
        sellerId:this.queryCondiValues.sellerId,
        startDate:this.queryCondiValues.startDate,
        endDate:this.queryCondiValues.endDate
      }})
    },
    toViewSheetAll(type){
      this.$router.push({ path:'/ViewSheetAll',query:{
        queryParams:JSON.stringify({
          sellerInfo:[{id:this.queryCondiValues.sellerId,name:this.queryCondiValues.sellerName}],
          startDate:this.queryCondiValues.startDate,
          endDate:this.queryCondiValues.endDate,
        }),
        sheetTabType:type
      }})
    },
    toViewSheets(type) {
      //参数参考selectCustomer页面
      this.$router.push({ path:'/ViewSheets',query:{
        supcustId:this.queryCondiValues.sellerId,
        supcustName:this.queryCondiValues.sellerName,
        startDate:this.queryCondiValues.startDate,
        sheetTabType:type
      }})
    },
    doPrint() {
      showConfirmDialog({
        title: '打印经营利润表',
        message:'确认打印经营利润表吗？',
        width:"320px"
      })
      .then(() => {
        // on confirm
      })
      .catch(() => {
        // on cancel
      });
    }
  }
  }
</script>

<style lang="less" scoped>
@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_acent_jcen: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_aun_flex: {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: unset;
};
.public_query_title {
  padding-top: 5px;
  border-bottom: 1px solid #f2f2f2;
  .public_query_titleSrc_item {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    vertical-align: top;
    border: none;
    padding: 10px 0 3px 0;
  }
  .public_query_titleSrc_checkbox {
    box-sizing: border-box;
    margin:10px 20px;
    display: flex;
    justify-content: space-between;
  }
}
.public_box{
  height: calc(100% - 80px);
}
.title {
  font-size: 16px;
  background: #ffffff;
  border-bottom: 1px solid #f2f2f2;
  padding: 5px 15px;
  position:relative .van-row {
    height: 100%;
    .van-col {
      height: 100%;
      @flex_acent_jcen();
    }
    .van-col:first-child {
      justify-content: flex-start;
    }
    .van-col:last-child {
      justify-content: flex-end;
    }
  }
}
/deep/.van-cell::after {
  border-bottom: 0px;
}
.van-hairline--top-bottom::after{
  border: 0px;
  border-bottom: 0px;
}
.receive_boxs {
  flex: auto;
  height: 0;
  overflow-y: auto;
  background-color: #ffffff;
  padding: 5px;
  font-size: 15px;
  // margin-bottom: 20px;
  .receive {
    clear: both;
    li {
      height: auto;
      clear: both;
      padding: 5px;
      border-bottom: 1px solid #f2f2f2;
      .receive_item {
        height: auto;
        @flex_acent_jbw();
        div {
          flex: 1;
          // text-align: center;
        }
        // div:first-child {
        //   text-align: left;
        // }
        .level_1 {
          font-weight: 700;
          text-align: left;
        }
        .level_2 {
          text-indent: 1em;
          text-align: left;
        }
        .level_3 {
          // text-indent: 2em;
          text-align: right;
          padding-right: 100px;
        }
        .level_4 {
          font-weight: bold;
          display: none;
        }
        .link {
          justify-content: flex-end;
          text-align: right;

          padding-right: 15px;
          flex: 1;
          display: flex;
          font-family: numfont;
          color:#C40000;
        }
        div:last-child {
          text-align: right;
          font-size: 20px;
          font-family: numfont;
          padding-right: 15px;
        }
      }
    }
  }
}
.black {
  fill: currentColor;
  // color: #757575;
  color: #8d8d8d;
}
.t_footer{
  width: 100px;
  height: 35px;
  overflow: hidden;
}
.footer{
  @flex_acent_jcen();
  background-color: #ffffff;
  height: 53px;
}
</style>